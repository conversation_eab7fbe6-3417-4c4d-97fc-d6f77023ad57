# DEV Configuration
NODE_ENV = production
# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=5000

FRONTEND_URL = https://app.thrivify.ai
ADMIN_FRONTEND_URL = https://admin.thrivify.ai
HR_FRONTEND_URL = https://hr.thrivify.ai


APP_ADMIN_MAIL='<EMAIL>'
APP_ADMIN_FIRST_NAME='Admin'
APP_ADMIN_LAST_NAME='Admin'
APP_ADMIN_PASSWORD='Thrivify@123'

# OTP
OTP_EXPIRY = 300000 # 5 minutes in milliseconds

# JWT Configuration
JWT_SECRET = secret12356789
JWT_LIFETIME = 18h
JWT_LIFETIME_ADMIN = 1h

BULK_EMAIL_BATCH_SIZE = 100
BULK_EMAIL_BATCH_DELAY = 1000

# Google OAuth 2.0 Configuration
GOOGLE_CLIENT_ID = 47814877615-q6irqsqgb71sfrie9p4bltohsik4n0p1.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET = 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
GOOGLE_CALLBACK_URL =https://api.thrivify.ai/auth/google/callback
# Access token expiry to be saved in db
ACCESS_TOKEN_EXPIRY = 64800000  # 18 hours in milliseconds
ADMIN_ACCESS_TOKEN_EXPIRY = 3600000 # 1 hour in milliseconds

# MySQL Configuration
DB_TYPE=mysql
DB_HOST=database-prod-4.c7s2amci8ky2.us-east-2.rds.amazonaws.com
DB_PORT=3306
DB_USERNAME=thrivifydbadmin
DB_PASSWORD=YfOdO3l37LxC^
DB_DATABASE=thrivifyproddb
DB_SYNCHRONIZE=false
DB_LOGGING=false
DB_ENTITIES=dist/**/*.entity{.ts,.js}
DB_MIGRATIONS=dist/migration/**/*{.ts,.js}
DB_SUBSCRIBERS=dist/subscriber/**/*{.ts,.js}

# Email Configuration
EMAIL_HOST=smtp.ethereal.email
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=a3PFUSrpMxJhuhrRmT

# Account Verification Email
EMAIL_SUBJECT = Account Verification Email
EMAIL_FROM = <EMAIL>
EMAIL_BODY = Account Verification Email

# FORGOT PASS EMAIL
FORGOT_EMAIL_SUBJECT = Forgot Password Email
FORGOT_EMAIL_FROM = <EMAIL>
FORGOT_EMAIL_BODY = Forgot Password Email

# HR Contact EMAIL
HR_CONTACT_EMAIL_SUBJECT = Inactive Enterprise Domain Notification
HR_CONTACT_EMAIL_FROM = <EMAIL>
HR_CONTACT_EMAIL_BODY = Inactive Enterprise Domain Notification

# HR Add User EMAIL
HR_ADD_USER_EMAIL_SUBJECT = Welcome to Thrivify AI, Your Account Has Been Created
HR_ADD_USER_EMAIL_FROM = <EMAIL>
HR_ADD_USER_EMAIL_BODY = Welcome to Thrivify AI, Your Account Has Been Created

# SES Configuration
AWS_SES_ACCESS_KEY_ID = ********************
AWS_SES_SECRET_ACCESS_KEY = abLGd82SFlzkXUVC9csyCsdBQ71+p+um/j9N/wW2

# AWS Configuraion
AWS_USER = AAKIAR2ABGMJWD7FR4TRV
AWS_PASS = BJsbvZnxkUa/U0SR/tGWjOqFJL0Iib+X8m+vJgknIWbt
AWS_ACCESS_KEY_ID = ********************
AWS_SECRET_ACCESS_KEY = UWAZ5fiNI38VAz+hpDkiv/OusjzAkz/4I6XZ9rky
AWS_REGION = us-east-2
AWS_BUCKET = thrivify-prod-images

# OPEN API
OPENAI_API_KEY = *******************************************************************************************************************************************************

# Ollama
OLAMMA_MODEL_ID = arn:aws:bedrock:us-east-2:************:inference-profile/us.meta.llama3-1-8b-instruct-v1:0
TEST_PROMPT = 'hi'


GET_YT_LINK_DESC_PROMPT = ``

### **DESCRIPTION:**
$$description
"


FITNESS_QUEST_PROMPT = "Generate a JSON array representing a 2-day professional wellness quest series for an office environment. Use the provided {LOCATION} (e.g., 'remote', 'office', or 'hybrid') and {LEVEL} (e.g., 'easy', 'medium', or 'hard') dynamically to guide the content of the quests. Ensure these values influence the complexity of the activity and any relevant considerations based on the environment but do not include {LOCATION} or {LEVEL} in the final JSON response.\n\nEach quest should follow this format:\n\n[\n  {\n    \"Quest Title\": \"Title\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for the activity.\",\n      \"2. Include wellness benefits of the activity.\",\n      \"3. Add relevant emojis to make it visually engaging.\",\n      \"4. Mention any specific considerations based on whether the activity is done in a 'work-from-home' or 'work-from-office' setting.\"\n    ],\n    \"Proof of Completion\": \"text, image, or video\"\n  },\n  {\n    \"Quest Title\": \"Title\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for the activity.\",\n      \"2. Include wellness benefits of the activity.\",\n      \"3. Add relevant emojis to make it visually engaging.\",\n      \"4. Mention any specific considerations based on whether the activity is done in a 'work-from-home' or 'work-from-office' setting.\"\n    ],\n    \"Proof of Completion\": \"text, image, or video\"\n  }\n]\n\nEnsure each activity is achievable within a workday and suitable for a professional setting. Provide detailed steps in the description, along with the wellness benefits of each activity. Add emojis to represent key actions, wellness benefits, or related themes directly within the quest description. For proof of completion, specify one of the following ENUM values: text, image, or video. Avoid any strenuous activities, inappropriate content, or elements that could disrupt the work environment. Ensure there are only 2 quests in the generated array."

PHOTOGRAPHY_QUEST_PROMPT = "Generate a JSON array representing a 2-day photography quest series tailored for an office environment. The quests should dynamically adapt to the work location ({LOCATION}) and difficulty level ({LEVEL}) to guide the content. Use the provided values to influence the complexity of the photo composition or explanation without explicitly including {LOCATION} or {LEVEL} in the final JSON response.\n\nEach quest should follow this format:\n\n[\n  {\n    \"Quest Title\": \"<title>\",\n    \"Quest Description\": [\n      \"1. <description>\",\n      \"2. <description>\",\n      \"3. <description>\",\n      \"4. <description>\"\n    ],\n    \"Proof of Completion\": \"image\"\n  },\n  {\n    \"Quest Title\": \"<title>\",\n    \"Quest Description\": [\n      \"1. <description>\",\n      \"2. <description>\",\n      \"3. <description>\",\n      \"4. <description>\"\n    ],\n    \"Proof of Completion\": \"image\"\n  }\n]\n\nEnsure each quest is engaging, professional, and designed to encourage creativity while maintaining appropriateness for a work setting. Use relevant emojis in the quest descriptions to enhance visual appeal. Both quests should align with the photography theme, and their descriptions should reflect adjustments based on different work environments (remote, hybrid, or in-office) without explicitly mentioning these variables."

SHORTS_VIDEO_QUEST_PROMPT = "Create a JSON object representing a wellness video quest for a 2-day series. Each object should include the following fields:\n- Quest Title: A short and engaging title for the quest.\n- Quest Description: A list of step-by-step instructions for creating a 30-second video, including:\n  1. Suggested ideas for the activity.\n  2. Highlighted wellness benefits.\n  3. Tips for making the video work-appropriate, concise, and visually appealing.\n  4. Adjustments for remote, hybrid, or in-office environments.\n- Proof of Completion: Always 'video'.\nGenerate two quests with this format:\n\n[\n  {\n    \"Quest Title\": \"Share Your Mindfulness Tip\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for creating a 30-second video sharing a wellness tip. Suggested ideas include mindfulness exercises 🧘‍♂️, desk stretches 🧎‍♀️, or quick breathing techniques 🌬️.\",\n      \"2. Highlight the wellness benefits such as reducing stress, boosting focus, or improving physical health.\",\n      \"3. Provide tips for making the video work-appropriate, concise, and visually appealing with a balance of creativity and professionalism ✨.\",\n      \"4. Include considerations for the activity, adjusting for 'work-from-home' (e.g., home-friendly stretches) or 'work-from-office' (e.g., workplace mindfulness). Difficulty level: easy.\"\n    ],\n    \"Proof of Completion\": \"video\"\n  },\n  {\n    \"Quest Title\": \"Showcase Your Self-Care Routine\",\n    \"Quest Description\": [\n      \"1. Step-by-step instructions for creating a 30-second video showcasing a creative self-care routine. Suggested ideas include a hydration reminder 💧, a walk around the block 🚶‍♀️, or a gratitude exercise 📝.\",\n      \"2. Emphasize the wellness benefits, such as maintaining energy, promoting positivity, or improving mental clarity.\",\n      \"3. Provide tips for keeping the video lighthearted and professional, with a focus on short and engaging content.\",\n      \"4. Include specific guidelines tailored to the activity, such as home-friendly adaptations (e.g., a gratitude journaling spot) or in-office adjustments (e.g., discreet self-care techniques). Difficulty level: medium.\"\n    ],\n    \"Proof of Completion\": \"video\"\n  }\n]\n\nEnsure activities are easy to complete within a workspace while promoting wellness and engagement."

PUZZLES_QUEST_PROMPT = "Generate a JSON array representing a 2-day office-friendly brain puzzle or time-based challenge series suitable for an office environment (remote, hybrid, or in-office). This series should consist of exactly two quests, one for each day. Each object in the array should follow this format:\n\n[\n  {\n    \"Quest Title\": \"Brain Puzzle Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of brain puzzle activity, such as a riddle or logic puzzle to be solved within 5 minutes. Include instructions for the puzzle and emphasize creative thinking. For example, 'Solve this riddle: I am taken from a mine, and shut up in a wooden case, from which I am never released, and yet I am used by almost every person. What am I?' The answer should be 'pencil.'\",\n      \"2. Add emojis like 🧠, ✏️, and ⏱️ to make the description engaging and visually appealing.\"\n    ],\n    \"Proof of Completion\": \"text\"\n  },\n  {\n    \"Quest Title\": \"Brain Puzzle Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of brain puzzle activity, encouraging users to solve a quick logic puzzle. For example, 'You have two ropes. Each rope has the property that if you light it at one end, it takes exactly one hour to burn to the other end. However, the ropes do not burn at a uniform rate (for example, half the rope could burn in 10 minutes, and the rest could take 50 minutes). How can you use these two ropes to time 45 minutes?'\",\n      \"2. Add emojis like 🧩, 🔥, and ⏳ to enhance engagement and make the description fun and professional.\"\n    ],\n    \"Proof of Completion\": \"text\"\n  }\n]\n\nPlease do not include any lines describing collaborative settings, motivational hints, or environmental adaptations (e.g., 'Remote: post in chat', 'Don’t overthink', or similar). Only include puzzle instructions and emojis. The challenges should be professional, brief, creative, and work-safe. Return exactly 2 quests only."


CODING_QUEST_PROMPT = "Generate a JSON array representing a 2-day coding challenge series suitable for an office environment. This series should consist of exactly two quests, one for each day. Each object in the array should follow this format:\n\n[\n  {\n    \"Quest Title\": \"Coding Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of the coding challenge, ensuring it can be completed within 15-30 minutes. For example, 'Write a function that takes a string and returns the same string reversed. Avoid using built-in reverse methods.'\",\n      \"2. Add relevant emojis like 💻, ⏳, and ⚡ to enhance engagement and excitement.\",\n      \"3. Ensure the challenge is **adaptable** to different programming languages (Python, JavaScript, Java, etc.), allowing participants to use their preferred language.\",\n      \"4. Provide dynamic considerations based on the work environment (remote, hybrid, or in-office), ensuring all participants can contribute equally.\",\n      \"5. Emphasize problem-solving, algorithmic thinking, and efficiency, while keeping the challenge **beginner to intermediate-friendly**.\"\n    ],\n    \"Proof of Completion\": \"code snippet\"\n  },\n  {\n    \"Quest Title\": \"Coding Quest Title\",\n    \"Quest Description\": [\n      \"1. Description of the second coding challenge, requiring a **slightly higher level of logic** than the first quest but still solvable within 15-30 minutes. For example, 'Write a function that checks if a given number is a prime number. Optimize your solution for large inputs.'\",\n      \"2. Add engaging emojis like 🚀, 🤖, and 🏆 to make the task visually appealing.\",\n      \"3. Ensure the challenge remains **language-agnostic**, allowing flexibility for different tech stacks.\",\n      \"4. Provide dynamic considerations based on the work environment, ensuring the activity is fun yet non-disruptive to workflow.\",\n      \"5. The challenge should encourage **efficient problem-solving, debugging skills, and coding best practices** while maintaining an office-friendly approach.\"\n    ],\n    \"Proof of Completion\": \"code snippet\"\n  }\n]\n\nEach quest should feature a concise, engaging coding task that can be completed within a workday. The challenges should be professional, fun, and suitable for an office environment, encouraging logical thinking, problem-solving, and efficiency without disrupting workflow. The proof of completion should be specified as an ENUM value, with \"code snippet\" as the method of submission.\n\nUse emojis to highlight creativity, logic, and coding expertise. Adapt each challenge to be inclusive of remote, hybrid, or in-office work environments, and adjust the difficulty to {LEVEL}, ensuring it remains appropriate for a professional setting.\n\nAvoid overly complex, time-consuming, or disruptive problems. Ensure there are exactly 2 quests in the generated array. 🚀💻🔥"

MCQ_QUEST_PROMPT= "You are an expert at creating multiple-choice questions. Generate 5 multiple-choice questions based on the following content. Each question should test understanding of key concepts and be at {DIFFICULTY_LEVEL} difficulty level.\n\nContent to generate questions from:\n{CONTENT}\n\nDifficulty Level: {DIFFICULTY_LEVEL}\n\nInstructions:\n1. Create exactly ${validatedNumQuestions} multiple-choice questions\n2. Each question must have exactly 5 options (numbered from 0-4)\n3. Each question must have exactly one correct answer\n4. All questions should be at {DIFFICULTY_LEVEL} difficulty level\n5. Make sure all options are MEANINGFUL and DIRECTLY RELATED to the content\n6. Options should be specific answers, not generic placeholders like \"Option A\"\n7. NEVER refer to "the text" or "the content" in your questions - write questions that directly address the subject matter\n8. Frame questions as if you're testing knowledge about the subject itself, not about what was mentioned in some text\n9. Adjust question complexity based on the specified difficulty level:\n   - Easy: Basic concepts and straightforward questions\n   - Intermediate: Moderate complexity requiring some analysis\n   - Hard: Complex concepts requiring deep understanding\n   - Very Hard: Advanced concepts requiring critical thinking\n10. Return your response EXACTLY as a valid JSON object with the following structure:\n\n{\n  \"questions\": [\n    {\n      \"question\": \"[A specific question about the subject matter]\",\n      \"options\": [\"A specific option related to content\", \"Another specific option\", \"Third specific option\", \"Fourth specific option\", \"Fifth specific option\"],\n      \"correctAnswers\": [2],\n      \"difficulty\": \"{DIFFICULTY_LEVEL}\"\n    }\n  ]\n}\n\nYour response must be ONLY the JSON object above, with no additional text or explanation."


#Analysis Prompts

TEXT_ANALYSIS_SCORE_PROMPT = <|begin_of_text|><|start_header_id|>system<|end_header_id|>
  You are an AI evaluator. Compare the user's answer with the description and return an integer score between 100 and 1000 based on relevance and quality.
  Respond with only a plain integer. Do not include any special characters, escape sequences, or extra text.
  
  Description: {description}
  Answer: {answer}
 

TEXT_ANALYSIS_SUGGESTION_PROMPT = 
  You are an AI evaluator. Suggest a clear improvement to the user's answer based on the description.
  Respond in one concise sentence using plain text only. Do not use special characters or metadata.
  
  Description: {description}
  Answer: {answer}
  

TEXT_ANALYSIS_IMPROVEMENT_PROMPT = 
  You are an AI evaluator. Briefly list key improvements needed in the user's answer based on the description.
  Respond in a single plain text sentence without using special characters or metadata.
  <|eot_id|><|start_header_id|>user<|end_header_id|>
  Description: {description}
  Answer: {answer}
  

# MCQ Analysis Prompts
MCQ_ANALYSIS_IMPROVEMENT_PROMPT = 
  You are an AI evaluator. Identify specific improvements needed in a user's answer to a multiple-choice question.
  Respond with a concise list in one sentence only. Do not explain your reasoning. Do not include any extra text.
 
  Question: {question}
  Correct Answers: {correctAnswers}
  User's Answer: {answer}
  

MCQ_ANALYSIS_SUGGESTION_PROMPT = 
  You are an AI evaluator. Provide a clear and actionable suggestion for improving a user's answer to a multiple-choice question.
  Respond in one concise sentence only. Do not explain your reasoning. Do not include any extra text.
 
  Question: {question}
  Correct Answers: {correctAnswers}
  User's Answer: {answer}
  

MCQ_ANALYSIS_SCORE_PROMPT =
  You are an AI evaluator. Analyze a user's answer to a multiple-choice question and provide a quality score between 100 and 1000.
  Respond with a single integer only. Do not explain your answer. Do not include any extra text. One word only.
  <|eot_id|><|start_header_id|>user<|end_header_id|>
  Question: {question}
  Correct Answers: {correctAnswers}
  User's Answer: {answer}
  

# Enhanced MCQ Analysis Prompts
MCQ_WHOLE_QUEST_EVALUATION_PROMPT =
You are a professional performance analyst conducting an objective evaluation of an employee's assessment results.

Assessment Details:
- Title: "{questTitle}"
- Subject: "{questDescription}"
- Total Questions: {totalQuestions}
- Correct Answers: {correctAnswers}
- Performance Score: {correctPercentage}%

Question Analysis:

"{questionAnalysis}"

Please provide a formal, data-driven evaluation suitable for HR analytics that includes:
1. A numeric performance rating between 100 and 1000 reflecting overall proficiency
2. A structured analysis of demonstrated strengths based on correct answers
3. A structured analysis of knowledge gaps based on incorrect answers
4. Professional development recommendations based on assessment performance

Your evaluation should:
- Use professional, objective language appropriate for HR documentation
- Focus on demonstrated knowledge areas rather than personal characteristics
- Provide substantive, specific insights rather than vague generalizations
- Avoid subjective language or exaggerated praise/criticism
- Format the evaluation for clarity and readability in professional reports

Respond with a valid JSON object structured as follows:
{
  "analysisScore": <numeric score>,
  "strength": "<professional analysis of demonstrated knowledge areas>",
  "weakness": "<objective analysis of knowledge gaps requiring attention>", 
  "recommendation": "<specific, actionable professional development recommendations>"
}