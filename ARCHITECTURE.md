# Thrivify Backend Architecture Documentation

## Overview

Thrivify Backend is a comprehensive enterprise application built with NestJS that provides quest management, AI-powered content generation, social features, and analytics for employee engagement platforms. The system supports multiple user roles (Users, HR, Admins) and integrates with various external services for enhanced functionality.

## System Architecture

### Core Technology Stack

- **Framework**: NestJS (Node.js)
- **Database**: MySQL with TypeORM
- **Authentication**: JWT with Google OAuth2 integration
- **File Storage**: AWS S3
- **Email Service**: AWS SES
- **AI Services**: AWS Bedrock (Llama 3.1), OpenAI GPT-4
- **PDF Processing**: Nova Service (External)
- **Deployment**: Multi-environment support (dev, prod, local)

### Application Structure

#### Main Application (Port 4000)
The primary backend service handling all API requests and business logic.

#### Background Services (Port 5001)
Dedicated cron job service for scheduled tasks and background processing.

## Core Modules

### 1. User Management
- **User Module**: User registration, authentication, profile management
- **Security Module**: JWT token management, authentication guards
- **Google Auth Module**: OAuth2 integration for social login

### 2. Quest System
- **Quest Module**: Core quest management functionality
- **AI Quest Module**: AI-powered quest generation using LLM services
- **Quest Types**: Categorization and classification of different quest types
- **MCQ Generation**: Multiple-choice question creation and management

### 3. Social Features
- **Feed Module**: Social posting, content sharing
- **Friends Module**: Social connections and friend requests
- **Comments & Interactions**: User engagement features
- **Leaderboard Module**: Competitive rankings and achievements

### 4. Enterprise Features
- **HR Module**: Human resources management and oversight
- **Admin Module**: System administration and configuration
- **Reports Module**: Analytics and reporting capabilities
- **Products Module**: Enterprise product management

### 5. Supporting Systems
- **Third Party Module**: External service integrations
- **Logger Module**: Custom logging and monitoring
- **Notifications Module**: User notification system
- **Cron Job Module**: Scheduled task management

## Database Architecture

### Core Entities

#### User Management
- **UserEntity**: User profiles, authentication data, preferences
- **EnterpriseEntity**: Organization/company information
- **AccessTokenEntity**: JWT token management
- **RoleEntity & PermissionEntity**: Role-based access control

#### Quest System
- **QuestEntity**: Quest definitions, metadata, and configuration
- **QuestParticipantEntity**: User-quest participation tracking
- **QuestMediaEntity**: Quest-related media assets
- **MCQQuestionEntity**: Multiple-choice questions
- **QuestTypesEntity**: Quest categorization

#### Social Features
- **FeedEntity**: Social posts and content
- **CommentEntity**: User comments on feeds
- **InteractionEntity**: Likes, reactions, and engagements
- **FriendRequestEntity**: Social connection management

#### System Entities
- **NotificationsEntity**: User notifications
- **ReportEntity**: Content moderation and reporting
- **UserCreditsEntity**: Reward and credit system
- **LeaderboardEntity**: Ranking and achievement tracking

## External Integrations

### AWS Services
- **S3**: File storage for media assets, quest materials, and user uploads
- **SES**: Email delivery for notifications and system communications
- **Bedrock**: LLM service using Llama 3.1 for AI quest generation and analysis
- **RDS**: MySQL database with read replica support for production

### Third-Party APIs
- **OpenAI**: GPT-4 integration for advanced AI features
- **Google OAuth2**: Social authentication
- **Nova Service**: External PDF parsing and processing

## Key Features

### AI-Powered Quest Generation
- Automated quest creation using AWS Bedrock (Llama 3.1)
- Quest analysis and scoring
- MCQ generation with AI assistance
- Content suggestion and improvement recommendations

### Multi-Tenant Enterprise Support
- Organization-based user management
- Department-based quest assignment
- Enterprise-specific configurations
- Role-based access control (User, HR, Admin)

### Social Engagement Platform
- Feed-based content sharing
- Friend connections and social interactions
- Leaderboards and competitive elements
- Comment and reaction systems

### Analytics and Reporting
- User engagement metrics
- Quest completion tracking
- Performance analytics
- Custom reporting for HR and Admin users

## Security Features

- JWT-based authentication with configurable expiration
- Role-based access control (RBAC)
- Enterprise domain validation
- Secure file upload and storage
- API rate limiting and validation
- Environment-specific configurations

## Deployment Architecture

### Environment Support
- **Development**: Local development with dev database
- **Production**: AWS RDS with read replicas, production S3 buckets
- **Local**: Local MySQL for development testing

### Configuration Management
- Environment-specific .env files
- Centralized configuration service
- Secure credential management

### Background Processing
- Separate cron job service for scheduled tasks
- Quest pool refill automation
- Notification processing
- Analytics data aggregation

## API Documentation

The application includes comprehensive Swagger/OpenAPI documentation available at `/api` endpoint, providing:
- Interactive API exploration
- Request/response schemas
- Authentication requirements
- Endpoint documentation

## Monitoring and Logging

- Custom logger service with structured logging
- Error tracking and exception handling
- Performance monitoring
- Database query optimization
- Request/response logging

## Scalability Considerations

- Modular architecture for horizontal scaling
- Database read replicas for improved performance
- Separate background processing service
- Stateless authentication with JWT
- Cloud-native AWS integration
- Efficient database indexing and optimization

This architecture supports a robust, scalable enterprise application capable of handling complex quest management, AI integration, and social engagement features while maintaining security and performance standards.
