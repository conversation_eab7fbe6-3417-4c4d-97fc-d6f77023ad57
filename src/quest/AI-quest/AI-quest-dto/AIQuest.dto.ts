import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ber,
  IsBoolean,
  IsE<PERSON>,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import {
  QUEST_DIFFICULTY_TYPES,
  QUEST_SCOPE,
  QuestEntity,
} from 'src/models/quest-entity';
import { QuestTypeDto } from '../../quest-dto';
import { Quest_EnterpriseDTO } from 'src/quest/quest-dto/quest.dto';
import { cleanQuestTitle } from 'src/utils/title-parser.utils';

export class AIQuestAssignedToUserDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest creator',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The email of the quest creator.',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The firstName of the quest creator.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the quest creator.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The avatar of the quest creator.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;
}

export class AIQuestDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The title of the quest',
    example: 'Quest for the Lost Artifact',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'A brief description of the quest',
    example: 'A thrilling adventure to find the lost artifact.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The number of completion credits awarded for this quest',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  completionCredits: number;

  @ApiProperty({
    description: 'The start date of the quest',
    type: String,
    format: 'date-time',
    example: '2024-10-22T10:00:00Z',
  })
  @IsString()
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({
    description: 'The end date of the quest',
    type: String,
    format: 'date-time',
    example: '2024-10-29T10:00:00Z',
  })
  @IsString()
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({
    description: 'The quest type of the quest',
    type: QuestTypeDto,
  })
  @IsNotEmpty()
  questType: QuestTypeDto;

  @ApiProperty({
    description: 'easy | intermediate | hard | very hard (optional)',
    enum: QUEST_DIFFICULTY_TYPES,
    example: 'easy | intermediate | hard | very hard (optional)',
    required: false,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES, {
    message:
      'Quest difficuty must be one of the following: easy, intermediate, hard or very hard',
  })
  @IsNotEmpty()
  difficulty: string;

  @ApiProperty({
    description: 'Indicates if the quest is active',
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @ApiProperty({
    description: 'Indicates if the quest is completed or not',
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isCompleted: boolean;

  @ApiProperty({
    description: 'Indicates if the quest is deleted',
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isDeleted: boolean;

  @ApiProperty({
    description: 'text | image | video (optional)',
    enum: QUEST_DIFFICULTY_TYPES,
    example: 'text | image | video  (optional)',
    required: false,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES, {
    message:
      'Quest submission type must be one of the following: text, image or video',
  })
  submissionMediaType: string;

  @ApiProperty({
    description: 'The user who created the quest',
    type: AIQuestAssignedToUserDTO,
  })
  @IsNotEmpty()
  assignedToUser: AIQuestAssignedToUserDTO;

  @ApiProperty({
    description: 'The enterprise associated with the quest',
    type: Quest_EnterpriseDTO,
  })
  @IsNotEmpty()
  enterprise: Quest_EnterpriseDTO;

  @ApiProperty({
    description: 'The worklocation associated with the quest',
    example: 'home',
  })
  @IsNotEmpty()
  workLocation: string;

  @ApiProperty({
    description: 'scope of quest',
    example: 'ai',
  })
  @IsNotEmpty()
  scope: QUEST_SCOPE;

  static transform(object: QuestEntity): AIQuestDTO {
    const transformedObj: AIQuestDTO = new AIQuestDTO();    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.title = cleanQuestTitle(object.title); // Clean AI-generated quest titles
    transformedObj.description = object.description;
    transformedObj.completionCredits = object.completionCredits;
    transformedObj.startDate = object.startDate;
    transformedObj.endDate = object.endDate;
    transformedObj.difficulty = object.difficulty;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.submissionMediaType = object.submissionMediaType;
    transformedObj.isActive = object.isActive;
    transformedObj.isCompleted = object.isCompleted;
    transformedObj.workLocation = object.workLocation;
    transformedObj.scope = object.scope;

    // Map QuestType
    if (object.questType) {
      transformedObj.questType = QuestTypeDto.transform(object.questType);
    }

    if (object.enterprise) {
      transformedObj.enterprise = Quest_EnterpriseDTO.transform(
        object.enterprise,
      );
    }

    // Map Creator
    if (object.assignedToUser) {
      transformedObj.assignedToUser = new AIQuestAssignedToUserDTO();
      transformedObj.assignedToUser.id = object.assignedToUser.id;
      transformedObj.assignedToUser.email = object.assignedToUser.email;
      transformedObj.assignedToUser.firstName = object.assignedToUser.firstName;
      transformedObj.assignedToUser.lastName = object.assignedToUser.lastName;
      transformedObj.assignedToUser.avatar = object.assignedToUser.avatar;
    }

    return transformedObj;
  }
}
