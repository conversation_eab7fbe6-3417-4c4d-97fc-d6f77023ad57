import { ApiProperty } from '@nestjs/swagger';
import {
  IsInt,
  IsDate,
  IsOptional,
  IsNotEmpty,
  IsString,
} from 'class-validator';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { QuestCompletionProofMediaDTO } from './PrivateQuestCompletionMedia.dto';
import { QuestEntity } from 'src/models/quest-entity';

class UserCredits_QuestDTO {
  @ApiProperty({
    description: 'The unique identifier for the quest record',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'quest title',
    example: 'sample title',
  })
  title: string;

  @ApiProperty({
    description: 'The quest completion media',
    type: [QuestCompletionProofMediaDTO],
  })
  completionMedia: QuestCompletionProofMediaDTO[];

  static transform(object: QuestEntity): UserCredits_QuestDTO {
    const transformedObj = new UserCredits_QuestDTO();

    transformedObj.id = object.id;
    transformedObj.title = object.title;

    if (object.completionMedia) {
      transformedObj.completionMedia = object.completionMedia.map((item) =>
        QuestCompletionProofMediaDTO.transform(item),
      );
    }

    return transformedObj;
  }
}

export class UserCreditsDTO {
  @ApiProperty({
    description: 'The unique identifier for the user credits record',
    example: 1,
  })
  id: number;

  @ApiProperty({
    description: 'The number of credits awarded to the user',
    example: 100,
  })
  @IsInt()
  credits: number;

  @ApiProperty({
    description: 'The number of credits awarded to the user',
    example: 100,
  })
  @IsString()
  answer: string;

  @ApiProperty({
    description: 'The number of analysis score awarded to the user',
    example: 100,
  })
  @IsInt()
  analysisScore: number;

  @ApiProperty({
    description: 'The date the credits were awarded',
    example: '2024-11-12T09:00:00Z',
  })
  @IsDate()
  date: Date;

  @ApiProperty({
    description:
      'The associated quest for which the credits are awarded (optional)',
    type: UserCredits_QuestDTO,
    required: false,
  })
  @IsOptional()
  quest?: UserCredits_QuestDTO;

  @ApiProperty({
    description:
      'The associated AI quest for which the credits are awarded (optional)',
    type: UserCredits_QuestDTO,
    required: false,
  })
  @IsOptional()
  AIQuest?: UserCredits_QuestDTO;

  static transform(object: UserCreditsEntity): UserCreditsDTO {
    const transformedObj = new UserCreditsDTO();

    // Map simple properties
    transformedObj.id = object.id;
    transformedObj.credits = object.credits;
    transformedObj.date = object.date;

    if (object.quest) {
      transformedObj.quest = UserCredits_QuestDTO.transform(object.quest);
    }

    return transformedObj;
  }
}
