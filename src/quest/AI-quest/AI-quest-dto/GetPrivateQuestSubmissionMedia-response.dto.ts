import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QuestCompletionProofMediaDTO } from './PrivateQuestCompletionMedia.dto';
import { ApiProperty } from '@nestjs/swagger';
import { UserCreditsDTO } from './UserCredits.dto';
import { IsObject, IsOptional, IsNumber, IsDate } from 'class-validator';

export class GetAIQuestSubmissionMediaResDTO extends BaseResponse {
  @ApiProperty({
    description: 'The quest completion media',
    example: 'sample submission caption',
  })
  caption: string;

  @ApiProperty({
    description: 'The quest completion media urls',
    example: ['url1', 'url2', 'url3'],
  })
  media_urls: string[];

  @ApiProperty({
    description: 'Metrics related to the quest submission',
    type: UserCreditsDTO,
  })
  @IsObject()
  @IsOptional()
  metrics?: UserCreditsDTO | {};

  @ApiProperty({
    description: 'The unique identifier for the user quest metrics record',
    example: 36,
  })
  @IsOptional()
  id?: number;

  @ApiProperty({
    description: 'The overall analysis score for the quest submission',
    example: 200,
  })
  @IsOptional()
  overallAnalysisScore?: number;

  @ApiProperty({
    description: 'The number of credits awarded for the quest completion',
    example: 211,
  })
  @IsOptional()
  credits?: number;

  @ApiProperty({
    description: 'The date the quest was completed',
    example: '2025-06-14T16:02:07.440Z',
  })
  @IsOptional()
  createdAt?: Date;

  @ApiProperty({
    description: 'The overall evaluation for MCQ quest including strengths, weaknesses, and recommendations',
    example: 'Strength: Correctly answered 1 question out of 5 with a score of 20%, Weakness: Incorrectly answered 4 out of 5 questions with a score of 80%, Recommendation: Review foundational knowledge of e-Mandate process to improve understanding of eNACH system.',
  })
  @IsOptional()
  evaluation?: string;
}
