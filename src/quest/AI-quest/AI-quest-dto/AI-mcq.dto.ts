import { ApiProperty } from '@nestjs/swagger';
import {
  <PERSON><PERSON><PERSON>y,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsO<PERSON>al,
  IsString,
  <PERSON>,
  <PERSON>,
  MinLength,
} from 'class-validator';
import { QUEST_DIFFICULTY_TYPES } from 'src/models/quest-entity';

export class CreateMCQQuestDTO {
  @ApiProperty({
    description: 'The title of the MCQ quest',
    example: 'JavaScript Basics Quiz',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'The description of the MCQ quest',
    example: 'Test your knowledge of JavaScript fundamentals',
  })
  @IsString()
  @IsNotEmpty()
  description: string;
  @ApiProperty({
    description: 'The content from which MCQs will be generated',
    example: 'JavaScript is a high-level, interpreted programming language...',
    minLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(500, {
    message:
      'Content must be at least 500 characters long. Please provide more detailed content.',
  })
  content: string;

  @ApiProperty({
    description: 'The difficulty level of the MCQs',
    enum: QUEST_DIFFICULTY_TYPES,
    example: QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES)
  difficulty: QUEST_DIFFICULTY_TYPES;

  @ApiProperty({
    description: 'Number of MCQ questions to generate (min: 5, max: 10)',
    example: 5,
    required: false,
    minimum: 5,
    maximum: 10,
  })
  @IsNumber()
  @Min(5)
  @Max(10)
  @IsOptional()
  numQuestions?: number;

  @ApiProperty({
    description: 'Credits awarded for completing the quest',
    example: 100,
    required: false,
  })
  @IsNumber()
  @IsOptional()
  completionCredits?: number;

  @ApiProperty({
    description: 'Tags to assign quest to specific users',
    type: [String],
    required: false,
    example: ['onboarding', 'training'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'End date of the quest',
    example: '2024-11-13T18:30:00.000Z',
    required: false,
  })
  @IsString()
  @IsOptional()
  endDate?: string;
}

export class MCQQuestionDTO {
  @IsString()
  question: string;

  @IsArray()
  options: string[];

  @IsArray()
  correctAnswers: number[];
  @IsEnum(['easy', 'intermediate', 'hard', 'very hard'])
  difficulty: string;
}

export class MCQSubmissionDTO {
  @IsArray()
  answers: {
    questionId: number;
    selectedOptions: number[];
  }[];
}
