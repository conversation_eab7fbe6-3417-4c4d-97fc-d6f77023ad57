import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Equal } from 'typeorm';
import { CodeCrafterQuestPoolEntity } from 'src/models/quest-entity/codecrafter_quest_pool.entity';
import { QUEST_DIFFICULTY_TYPES } from 'src/models/quest-entity/quest.entity';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';

interface BulkQuestDTO {
  'Quest Title': string;
  'Quest Description': string[];
  'Proof of Completion': string;
}

function cleanAndParseQuests(responseContent: string): BulkQuestDTO[] {
  const arrayMatch = responseContent.match(/\[\s*\{[\s\S]*?\}\s*\]/);
  if (arrayMatch) {
    try {
      return JSON.parse(arrayMatch[0]);
    } catch {}
  }
  const objectMatches = responseContent.match(/\{[\s\S]*?\}/g) || [];
  return objectMatches.map((objStr) => {
    const cleaned = objStr
      .replace(/(\w+)\s*:/g, (_, key) => `"${key}":`)
      .replace(/'/g, '"')
      .replace(/,\s*}/g, '}')
      .replace(/\r?\n/g, ' ');
    return JSON.parse(cleaned);
  });
}

@Injectable()
export class CodingQuestPoolRefillService {
  private readonly logger = new Logger(CodingQuestPoolRefillService.name);

  constructor(
    @InjectRepository(CodeCrafterQuestPoolEntity)
    private readonly poolRepo: Repository<CodeCrafterQuestPoolEntity>,
    private readonly awsLlamaIService: AWSLlamaAIService,
  ) {}

  async refill(
    difficulty: QUEST_DIFFICULTY_TYPES,
    count: number,
  ): Promise<void> {
    if (count <= 0) {
      this.logger.log(`No refill needed for ${difficulty}`);
      return;
    }

    await this.poolRepo.manager.transaction(async (tx) => {
      await tx
        .getRepository(CodeCrafterQuestPoolEntity)
        .createQueryBuilder('p')
        .setLock('pessimistic_write')
        .getMany();

      const raw = await this.awsLlamaIService.generateBulkCodingQuests(
        count,
        difficulty,
      );

      let candidates: BulkQuestDTO[];
      try {
        candidates = cleanAndParseQuests(raw);
      } catch (err) {
        this.logger.error('Bulk JSON parse failed', err);
        return;
      }

      const existing = await tx.find(CodeCrafterQuestPoolEntity, {
        where: { difficulty },
      });
      const titles = new Set(existing.map((p) => p.title));

      const toInsert = candidates
        .filter((q) => !titles.has(q['Quest Title']))
        .slice(0, count)
        .map((q) =>
          tx.create(CodeCrafterQuestPoolEntity, {
            difficulty,
            title: q['Quest Title'],
            description: q['Quest Description'].join('\n'),
            proofOfCompletion: q['Proof of Completion'] as any,
            assigned: false,
          }),
        );

      if (toInsert.length) {
        await tx.save(toInsert);
        this.logger.log(`Inserted ${toInsert.length} new ${difficulty} quests`);
      } else {
        this.logger.log(`No unique quests to insert for ${difficulty}`);
      }
    });
  }
}