import {
  BadRequestException,
  Injectable,
  InternalServerErrorException,
  NotFoundException,
} from '@nestjs/common';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { DataSource, EntityManager, Repository, In, Between } from 'typeorm';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import {
  PARTICIPANT_STATUS,
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { UserEntity } from 'src/models/user-entity';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import { UserCreditsDTO } from './AI-quest-dto/UserCredits.dto';
import { UserCreditsEntity } from 'src/models/credits-entity';
import {
  AIQuestDTO,
  CreateAIQuestResDTO,
  GetAIQuestSubmissionMediaResDTO,
  getSingleAIQuestByIdResDTO,
  submitAIQuestReqDTO,
  SubmitAIQuestResDTO,
} from './AI-quest-dto';
import {
  QUEST_SCOPE,
  QUEST_DIFFICULTY_TYPES,
  QuestCompletionTrackingEntity,
} from 'src/models/quest-entity';
import { EnterpriseEntity } from '../../models/user-entity/enterprise.entity';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';
import { Request } from 'express';
import { QuestDTO } from '../quest-dto';
import { GetAllUserQuestsQueryFilterInterface } from './interfaces';
import { CreateMCQQuestDTO, MCQSubmissionDTO } from './AI-quest-dto/AI-mcq.dto';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';
import { AWSLlamaAIService } from 'src/third-party/aws/llama/generate-quest/generate-quest.service';
import {
  UserMCQQuestionMetricsEntity,
  UserQuestMetricsEntity,
} from 'src/models/metrics-entity';

@Injectable()
export class AIQuestService {
  constructor(
    private readonly s3Service: S3Service,
    private readonly dataSource: DataSource,
    private readonly logger: CustomLogger,
    private readonly leaderboardUtilsService: LeaderboardUtilsService,
    private readonly leaderboardService: LeaderboardService,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestCompletionProofMediaEntity)
    private readonly questCompletionMediaRepo: Repository<QuestCompletionProofMediaEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,

    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,

    @InjectRepository(UserQuestMetricsEntity)
    private readonly userQuestMetricsRepo: Repository<UserQuestMetricsEntity>,

    @InjectRepository(UserMCQQuestionMetricsEntity)
    private readonly userMCQQuestionMetricsRepo: Repository<UserMCQQuestionMetricsEntity>,

    private readonly aWSLlamaAIService: AWSLlamaAIService,
  ) {}

  async getAllUserQuests(
    user: UserEntity,
    req: Request,
    filterData: GetAllUserQuestsQueryFilterInterface,
  ): Promise<CreateAIQuestResDTO> {
    const { scope } = filterData;

    try {
      const currentDate = new Date();
      currentDate.setHours(0, 0, 0, 0);

      const activeAIquestsForToday = await this.questRepo.find({
        where: {
          assignedToUser: { id: user.id },
          isActive: true,
          isDeleted: false,
          questType: { id: In(user.selectedQuestTypes.map((type) => type.id)) },
          startDate: currentDate,
        },
        relations: [
          'assignedToUser',
          'enterprise',
          'questType',
          'media',
          'completionMedia',
        ],
        order: {
          createdAt: 'DESC',
        },
      });

      const aiQuestsResp = activeAIquestsForToday.map((item) =>
        AIQuestDTO.transform(item),
      );

      const epActiveQuests = await this.questRepo.find({
        where: {
          enterprise: { id: user.enterprise.id },
          scope: QUEST_SCOPE.ENTERPRISE,
          isActive: true,
          isDeleted: false,
          questType: { id: In(user.selectedQuestTypes.map((type) => type.id)) },
        },
        relations: ['enterprise', 'questType', 'media', 'participants'],
        order: {
          createdAt: 'DESC',
        },
      });

      const filteredEpQuests = epActiveQuests.filter((quest) => {
        if (!quest.tags || quest.tags.length === 0) return true;
        return user.tags && quest.tags.some((tag) => user.tags.includes(tag));
      });

      const epQuestsResp = await Promise.all(
        filteredEpQuests.map(async (quest) => {
          const participant = await this.participantRepo.findOne({
            where: {
              user: { id: user.id },
              quest: { id: quest.id },
            },
          });

          const isCompleted =
            participant?.status === PARTICIPANT_STATUS.COMPLETED;

          return QuestDTO.transform(quest, true, isCompleted);
        }),
      );

      if (scope) {
        return {
          error: false,
          status: true,
          nbHits:
            scope === QUEST_SCOPE.AI
              ? aiQuestsResp.length
              : epQuestsResp.length,
          quests: scope === QUEST_SCOPE.AI ? aiQuestsResp : epQuestsResp,
        };
      }

      return {
        error: false,
        status: req['status'],
        nbHits: aiQuestsResp.length + epQuestsResp.length,
        quests: [...aiQuestsResp, ...epQuestsResp],
      };
    } catch (error) {
      this.logger.error(error);
      throw new BadRequestException('Error retrieving quests.');
    }
  }

  async getAIQuestById(
    questId: string,
    user: UserEntity,
  ): Promise<getSingleAIQuestByIdResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        assignedToUser: { id: user.id },
        id,
        isDeleted: false,
      },
      relations: ['assignedToUser', 'questType', 'media', 'completionMedia'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found.');
    }

    const resp = AIQuestDTO.transform(quest);

    return {
      error: false,
      quest: resp,
    };
  }

  async submitToAIQuest(
    user: UserEntity,
    AIQuestId: string,
    submission_medias: Express.Multer.File[],
    completionData: submitAIQuestReqDTO,
  ): Promise<SubmitAIQuestResDTO> {
    try {
      const id = this.validateAndGetQuestId(AIQuestId);
      const quest = await this.questRepo.findOne({
        where: {
          assignedToUser: { id: user.id },
          id,
        },
        relations: ['completionMedia', 'assignedToUser', 'questType'],
      });

      if (!quest || quest.isDeleted === true) {
        throw new BadRequestException('Quest not found.');
      }

      if (!quest.isActive) {
        throw new BadRequestException('Quest is not currently active.');
      }

      if (quest.isCompleted) {
        throw new BadRequestException('Quest is already completed by you.');
      }

      await this.checkSubmissionMediaTypes(
        submission_medias,
        quest.submissionMediaType,
      );

      const userCredit = await this.dataSource.transaction(async (manager) => {
        const { caption, completeDate } = completionData;

        this.leaderboardUtilsService.validateDateFormat(completeDate);

        let completionMedias: QuestCompletionProofMediaEntity[];

        if (submission_medias?.length > 0) {
          completionMedias = await Promise.all(
            submission_medias.map(async (item) => {
              let completionMedia = new QuestCompletionProofMediaEntity();

              completionMedia.caption = caption;
              completionMedia.quest = quest;
              completionMedia.userToSubmit = user;

              const uploadedFile = await this.s3Service.uploadFile(item);
              completionMedia.url = uploadedFile.Location;

              const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

              const mediaType = this.determineMediaType(fileExt);

              completionMedia.type = SUBMISSION_MEDIA_TYPES[mediaType];

              return completionMedia;
            }),
          );

          completionMedias = await manager.save(completionMedias);
        } else {
          let completionMedia = new QuestCompletionProofMediaEntity();

          completionMedia.caption = caption;
          completionMedia.quest = quest;
          completionMedia.userToSubmit = user;
          completionMedia.type =
            SUBMISSION_MEDIA_TYPES[quest.submissionMediaType.toUpperCase()];

          completionMedias = [await manager.save(completionMedia)];
        }

        if (completionMedias.length > 0) {
          quest.completionMedia = completionMedias;
        }

        quest.isCompleted = true;

        await manager.save(quest);
        await manager.save(user);

        const userCredit = await this.createUserCredit(
          manager,
          user,
          quest,
          completeDate,
          completionData.caption,
        );

        await this.trackQuestCompletion(manager, user, quest);
        return userCredit;
      });

      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        userCredit.credits,
        userCredit,
      );

      const userCreditResp = UserCreditsDTO.transform(userCredit);

      return {
        error: false,
        msg: 'Quest submitted successfully !!',
        userCredit: userCreditResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async createUserCredit(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
    completeDate: string,
    answer: string | MCQSubmissionDTO,
  ): Promise<UserCreditsEntity> {
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      const credits = quest.completionCredits;
      if (credits === null || credits === undefined || isNaN(credits)) {
        console.error('Invalid credits value:', credits);
        newUserCredit.credits = 0;
      } else {
        newUserCredit.credits = Number(credits);
      }

      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = quest?.id
        ? ({
            id: quest.id,
          } as QuestEntity)
        : null;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      await this.createUserQuestMetrics(quest, user, answer, manager);

      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting quest,Please try again later....',
      );
    }
  }

  async createUserCreditWithMCQDetails(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
    completeDate: string,
    answer: MCQSubmissionDTO,
    questionsWithAnswers: any[],
    scorePercentage?: number,
  ): Promise<UserCreditsEntity> {
    if (!user) {
      throw new BadRequestException('User is null or undefined');
    }
    if (!quest) {
      throw new BadRequestException('Quest is null or undefined');
    }
    if (!user.enterprise) {
      throw new BadRequestException('User enterprise is missing');
    }
    if (!quest.questType) {
      throw new BadRequestException('Quest type is missing');
    }

    try {
      const newUserCredit = new UserCreditsEntity();

      const baseCredits = quest.completionCredits;

      if (
        baseCredits === null ||
        baseCredits === undefined ||
        isNaN(baseCredits)
      ) {
        console.error('Invalid credits value:', baseCredits);
        newUserCredit.credits = 0;
      } else {
        if (scorePercentage !== undefined && scorePercentage !== null) {
          const calculatedCredits = Math.round(
            (Number(baseCredits) * scorePercentage) / 100,
          );
          newUserCredit.credits = calculatedCredits;
        } else {
          newUserCredit.credits = Number(baseCredits);
        }
      }

      newUserCredit.enterprise = {
        id: user.enterprise?.id ?? null,
      } as EnterpriseEntity;

      newUserCredit.user = {
        id: user?.id ?? null,
      } as UserEntity;

      newUserCredit.quest = quest?.id
        ? ({
            id: quest.id,
          } as QuestEntity)
        : null;

      newUserCredit.questType = quest.questType?.id
        ? ({
            id: quest.questType.id,
          } as QuestTypesEntity)
        : null;

      newUserCredit.date = new Date();
      newUserCredit.submissionDate = completeDate.split('T')[0];
      newUserCredit.submissionFullDate = completeDate;

      await this.createUserQuestMetrics(
        quest,
        user,
        answer,
        manager,
        newUserCredit.credits,
      );

      // Save and return
      const savedCredit = await manager.save(UserCreditsEntity, newUserCredit);

      return savedCredit;
    } catch (error) {
      this.logger.error(error);
      throw new InternalServerErrorException(
        'Something went wrong while submitting quest,Please try again later....',
      );
    }
  }

  async analyzeUserScore(
    quest: QuestEntity,
    answer: string | null,
  ): Promise<{
    analysisScore: number;
    aiSuggestion: string;
    improvementNeeded: string;
  }> {
    if (answer !== null) {
      const { aiSuggestion, analysisScore, improvementNeeded } =
        await this.aWSLlamaAIService.getQuestAnalysis(quest, answer);

      return {
        analysisScore,
        aiSuggestion,
        improvementNeeded,
      };
    } else {
      return {
        analysisScore: 100,
        aiSuggestion:
          'Consider adding more details and examples to improve your answer.',
        improvementNeeded:
          'Focus on providing more specific details and ensuring clarity in your response.',
      };
    }
  }

  async createUserQuestMetrics(
    quest: QuestEntity | any,
    user: UserEntity,
    answer: string | MCQSubmissionDTO,
    manager: EntityManager,
    awardedCredits?: number,
  ) {
    try {
      const userQuestMetrics = new UserQuestMetricsEntity();

      userQuestMetrics.quest = quest;

      userQuestMetrics.questType = quest.questType;
      userQuestMetrics.user = user;
      userQuestMetrics.enterprise = user.enterprise;

      if (awardedCredits !== undefined && awardedCredits !== null) {
        userQuestMetrics.credits = awardedCredits;
      } else if (
        quest.completionCredits !== undefined &&
        quest.completionCredits !== null
      ) {
        userQuestMetrics.credits = quest.completionCredits;
      } else {
        userQuestMetrics.credits = 0;
        console.warn(
          'No valid credits value found for quest metrics, using 0 as fallback',
        );
      }

      if (quest.submissionMediaType === SUBMISSION_MEDIA_TYPES.MCQ) {
        if (typeof answer !== 'string') {
          const mcqAnswer = answer as MCQSubmissionDTO;

          let correctAnswers = 0;
          let totalQuestions = 0;
          const questionsWithAnswers = [];

          totalQuestions = quest.mcqQuestions ? quest.mcqQuestions.length : 0;

          if (quest.mcqQuestions) {
            for (const userAnswer of mcqAnswer.answers) {
              const question = quest.mcqQuestions.find(
                (q) => q.id === userAnswer.questionId,
              );
              if (question) {
                const isCorrect =
                  userAnswer.selectedOptions.length ===
                    question.correctAnswers.length &&
                  userAnswer.selectedOptions.every((opt) =>
                    question.correctAnswers.includes(opt),
                  );
                if (isCorrect) {
                  correctAnswers++;
                }

                questionsWithAnswers.push({
                  question: question.question,
                  options: question.options,
                  correctAnswers: question.correctAnswers,
                  userAnswers: userAnswer.selectedOptions,
                  isCorrect: isCorrect,
                });
              }
            }
          }

          const { analysisScore, evaluation } =
            await this.aWSLlamaAIService.getMCQQuestAnalysis(
              quest,
              correctAnswers,
              totalQuestions,
              questionsWithAnswers,
            );

          userQuestMetrics.overallAnalysisScore = analysisScore;
          userQuestMetrics.overallAISuggestion = JSON.stringify(evaluation);
          userQuestMetrics.overallImprovementNeeded =
            JSON.stringify(evaluation);

          userQuestMetrics.answer = JSON.stringify(mcqAnswer);
        }
      } else if (quest.submissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT) {
        if (typeof answer === 'string') {
          const { aiSuggestion, analysisScore, improvementNeeded } =
            await this.analyzeUserScore(quest, answer);

          userQuestMetrics.answer = answer;
          userQuestMetrics.overallAnalysisScore = analysisScore;
          userQuestMetrics.overallAISuggestion = aiSuggestion;
          userQuestMetrics.overallImprovementNeeded = improvementNeeded;
        }
      }

      await manager.save(UserQuestMetricsEntity, userQuestMetrics);
    } catch (error) {
      console.error({ error });
      throw error;
    }
  }

  async createUserQuestMetricsWithMCQDetails(
    quest: QuestEntity,
    user: UserEntity,
    answer: MCQSubmissionDTO,
    questionsWithAnswers: any[],
    manager: EntityManager,
  ) {
    try {
      const userQuestMetrics = new UserQuestMetricsEntity();
      userQuestMetrics.quest = quest;
      userQuestMetrics.questType = quest.questType;
      userQuestMetrics.user = user;
      userQuestMetrics.enterprise = user.enterprise;
      userQuestMetrics.credits = quest.completionCredits;

      let correctAnswers = 0;
      const totalQuestions = questionsWithAnswers.length;

      for (const questionData of questionsWithAnswers) {
        if (questionData.isCorrect) {
          correctAnswers++;
        }
      }

      const { analysisScore, evaluation } =
        await this.aWSLlamaAIService.getMCQQuestAnalysis(
          quest,
          correctAnswers,
          totalQuestions,
          questionsWithAnswers,
        );

      userQuestMetrics.overallAnalysisScore = analysisScore;
      userQuestMetrics.overallAISuggestion = JSON.stringify(evaluation);
      userQuestMetrics.overallImprovementNeeded = JSON.stringify(evaluation);

      userQuestMetrics.answer = JSON.stringify(answer);

      await manager.save(UserQuestMetricsEntity, userQuestMetrics);
    } catch (error) {
      console.error({ error });
      throw error;
    }
  }

  private async trackQuestCompletion(
    manager: EntityManager,
    user: UserEntity,
    quest: QuestEntity,
  ) {
    try {
      const trackingRepo = manager.getRepository(QuestCompletionTrackingEntity);

      let tracking = await trackingRepo.findOne({
        where: {
          user: { id: user.id },
          questType: { id: quest.questType.id },
        },
        relations: ['user', 'questType'],
      });

      if (!tracking) {
        tracking = new QuestCompletionTrackingEntity();
        tracking.user = user;
        tracking.questType = quest.questType;
        tracking.completedQuestsCount = 1;
        tracking.currentDifficulty = QUEST_DIFFICULTY_TYPES.EASY;
        tracking.easyQuestsCompleted = 15;
        tracking.intermediateQuestsCompleted = 25;
        tracking.hardQuestsCompleted = 35;
        tracking.veryHardQuestsCompleted = 45;
      } else {
        this.incrementDifficultyCount(tracking, quest.difficulty);
        tracking.completedQuestsCount++;
      }

      if (this.shouldUpgradeDifficulty(tracking)) {
        tracking.currentDifficulty = this.getNextDifficulty(
          tracking.currentDifficulty,
        );

        user.difficulty = tracking.currentDifficulty;
        await manager.save(user);
      }

      tracking.lastUpdated = new Date();

      // Save the updated tracking record
      await trackingRepo.save(tracking);
    } catch (error) {
      console.error('Error tracking quest completion:', error);
      throw new Error(`Failed to track quest completion: ${error.message}`);
    }
  }

  private incrementDifficultyCount(
    tracking: QuestCompletionTrackingEntity,
    difficulty: QUEST_DIFFICULTY_TYPES,
  ) {
    switch (difficulty) {
      case QUEST_DIFFICULTY_TYPES.EASY:
        tracking.easyQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.INTERMEDIATE:
        tracking.intermediateQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.HARD:
        tracking.hardQuestsCompleted++;
        break;
      case QUEST_DIFFICULTY_TYPES.VERY_HARD:
        tracking.veryHardQuestsCompleted++;
        break;
    }
  }

  private shouldUpgradeDifficulty(
    tracking: QuestCompletionTrackingEntity,
  ): boolean {
    switch (tracking.currentDifficulty) {
      case QUEST_DIFFICULTY_TYPES.EASY:
        return tracking.easyQuestsCompleted >= 15;
      case QUEST_DIFFICULTY_TYPES.INTERMEDIATE:
        return tracking.intermediateQuestsCompleted >= 25;
      case QUEST_DIFFICULTY_TYPES.HARD:
        return tracking.hardQuestsCompleted >= 35;
      case QUEST_DIFFICULTY_TYPES.VERY_HARD:
        return false;
      default:
        return false;
    }
  }

  private getNextDifficulty(
    currentDifficulty: QUEST_DIFFICULTY_TYPES,
  ): QUEST_DIFFICULTY_TYPES {
    const difficulties = [
      QUEST_DIFFICULTY_TYPES.EASY,
      QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
      QUEST_DIFFICULTY_TYPES.HARD,
      QUEST_DIFFICULTY_TYPES.VERY_HARD,
    ];

    const currentIndex = difficulties.indexOf(currentDifficulty);
    return currentIndex < difficulties.length - 1
      ? difficulties[currentIndex + 1]
      : currentDifficulty;
  }

  async getQuestSubmissionMedia(
    user: UserEntity,
    AIQuestId: string,
  ): Promise<GetAIQuestSubmissionMediaResDTO> {
    try {
      const id = this.validateAndGetQuestId(AIQuestId);

      const quest = await this.questRepo.findOne({
        where: {
          assignedToUser: { id: user.id },
          id,
        },
        relations: ['assignedToUser'],
      });

      if (!quest || quest.isDeleted === true) {
        throw new BadRequestException('Quest not found');
      }

      const completionMedias = await this.questCompletionMediaRepo.find({
        where: { userToSubmit: { id: user.id }, quest: { id: quest.id } },
      });

      if (!completionMedias || completionMedias.length <= 0) {
        return { error: false, caption: '', media_urls: [], metrics: {} };
      }

      const questMetric = await this.userQuestMetricsRepo.findOne({
        where: {
          quest: { id: parseInt(AIQuestId, 10) },
          user: { id: user.id },
        },
      });

      return {
        error: false,
        caption: completionMedias[0].caption,
        media_urls:
          quest.submissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT
            ? completionMedias.map((item) => item.url)
            : [],
        metrics: questMetric
          ? {
              id: questMetric.id,
              overallAnalysisScore: questMetric.overallAnalysisScore,
              credits: questMetric.credits,
              createdAt: questMetric.createdAt,
              evaluation: this.parseEvaluationForResponse(
                questMetric,
                quest.submissionMediaType,
              ),
            }
          : {},
      };
    } catch (error) {
      return { error: false, caption: '', media_urls: [], metrics: {} };
    }
  }

  validateAndGetQuestId(questId: string): number {
    const id = parseInt(questId, 10);

    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Quest id provided ${id}.`);
    }

    return id;
  }

  private async checkSubmissionMediaTypes(
    submission_medias: Express.Multer.File[],
    questSubmissionMediaType: string,
  ) {
    if (
      questSubmissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT &&
      submission_medias?.length > 0
    ) {
      throw new BadRequestException(
        'Provide only text for this quest submission.',
      );
    }

    if (
      questSubmissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT &&
      submission_medias?.length <= 0
    ) {
      throw new BadRequestException(
        'Please provided submission media for quest submission.',
      );
    }

    if (questSubmissionMediaType === SUBMISSION_MEDIA_TYPES.MIXED) {
      const allMediaTypesMatch = submission_medias.map((item) => {
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';

        const mediaType = this.determineMediaType(fileExt);

        return mediaType;
      });

      if (allMediaTypesMatch.length !== 1) {
        throw new BadRequestException(
          `All submission media type must be of same type`,
        );
      }
    } else {
      const allMediaTypesMatch = submission_medias.every((item) => {
        const fileExt = item.mimetype ? item.mimetype.split('/')[1] : '';
        const mediaType = this.determineMediaType(fileExt);
        return mediaType === questSubmissionMediaType;
      });

      if (!allMediaTypesMatch) {
        throw new BadRequestException(
          `All submission media type must be of type ${questSubmissionMediaType}`,
        );
      }
    }
  }

  private determineMediaType(fileExt: string): string {
    if (AllowedVideoExtensions.includes(fileExt)) {
      return SUBMISSION_MEDIA_TYPES.VIDEO;
    } else if (AllowedImageExtensions.includes(fileExt)) {
      return SUBMISSION_MEDIA_TYPES.IMAGE;
    }
    return SUBMISSION_MEDIA_TYPES.TEXT;
  }

  async createMCQQuest(
    createDto: CreateMCQQuestDTO,
    user: UserEntity,
  ): Promise<QuestEntity> {
    if (!user.enterprise) {
      throw new BadRequestException(
        'Only enterprise users can create MCQ quests',
      );
    }

    let endDate: Date;
    if (createDto.endDate) {
      endDate = new Date(createDto.endDate);
      if (isNaN(endDate.getTime())) {
        throw new BadRequestException('Invalid end date provided');
      }
      // Validate that end date is in the future
      if (endDate <= new Date()) {
        throw new BadRequestException('End date must be in the future');
      }
    } else {
      endDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
    }

    const quest = this.questRepo.create({
      title: createDto.title,
      description: createDto.description,
      difficulty: createDto.difficulty,
      submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      scope: QUEST_SCOPE.ENTERPRISE,
      isActive: true,
      startDate: new Date(),
      endDate: endDate,
      completionCredits: createDto.completionCredits || 100,
      enterprise: user.enterprise,
      creator: user,
      tags: createDto.tags || [],
    });

    return await this.questRepo.save(quest);
  }

  async submitMCQAnswers(
    questId: number,
    submission: MCQSubmissionDTO,
    user: UserEntity,
  ) {
    const quest = await this.questRepo.findOne({
      where: { id: questId },
      relations: ['mcqQuestions', 'assignedToUser', 'enterprise', 'questType'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    if (quest.scope !== QUEST_SCOPE.AI) {
      throw new BadRequestException(
        'This endpoint only handles AI-scope MCQ quests. Use enterprise quest endpoints for enterprise MCQs.',
      );
    }

    if (!quest.assignedToUser || quest.assignedToUser.id !== user.id) {
      throw new BadRequestException(
        'You are not authorized to submit answers for this quest',
      );
    }

    let correctAnswers = 0;
    let totalQuestions = quest.mcqQuestions.length;

    for (const answer of submission.answers) {
      const question = await this.mcqQuestionRepo.findOne({
        where: { id: answer.questionId },
      });

      if (!question) {
        throw new BadRequestException(
          `Question with ID ${answer.questionId} not found`,
        );
      }

      const invalidOptions = answer.selectedOptions.filter(
        (opt) => opt < 0 || opt >= question.options.length,
      );
      if (invalidOptions.length > 0) {
        throw new BadRequestException(
          `Invalid option indices: ${invalidOptions.join(', ')}`,
        );
      }

      const isCorrect =
        answer.selectedOptions.length === question.correctAnswers.length &&
        answer.selectedOptions.every((opt) =>
          question.correctAnswers.includes(opt),
        );

      if (isCorrect) {
        correctAnswers++;
      }
    }

    const score = (correctAnswers / totalQuestions) * 100;

    // Mark AI quest as completed
    quest.isCompleted = true;
    await this.questRepo.save(quest);

    const questionsWithAnswers = [];

    for (const answer of submission.answers) {
      const question = await this.mcqQuestionRepo.findOne({
        where: { id: answer.questionId },
      });

      const isCorrect =
        answer.selectedOptions.length === question.correctAnswers.length &&
        answer.selectedOptions.every((opt) =>
          question.correctAnswers.includes(opt),
        );

      questionsWithAnswers.push({
        question: question.question,
        options: question.options,
        correctAnswers: question.correctAnswers,
        userAnswers: answer.selectedOptions,
        isCorrect: isCorrect,
      });
    }

    const userCredit = await this.createUserCreditWithMCQDetails(
      this.dataSource.manager,
      user,
      quest,
      new Date().toISOString(),
      { answers: submission.answers } as MCQSubmissionDTO,
      questionsWithAnswers,
      score,
    );

    try {
      const existingMetrics = await this.userQuestMetricsRepo.findOne({
        where: {
          user: { id: user.id },
          quest: { id: questId },
        },
      });

      if (existingMetrics) {
        existingMetrics.answer = JSON.stringify(submission);
        await this.userQuestMetricsRepo.save(existingMetrics);
      }
    } catch (error) {}

    if (userCredit.credits > 0) {
      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        userCredit.credits,
        userCredit,
      );
    }

    return {
      score,
      correctAnswers,
      totalQuestions,
      passed: score >= 70,
      userCredit: UserCreditsDTO.transform(userCredit),
    };
  }

  async getMCQQuestions(questId: number, user: UserEntity) {
    const quest = await this.questRepo.findOne({
      where: {
        id: questId,
        enterprise: { id: user.enterprise.id },
        submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      },
      relations: ['mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('MCQ quest not found');
    }

    const isHR = user.roles?.some((role) => role.value === ROLE_VALUES.HR);

    if (!isHR) {
      if (quest.tags && quest.tags.length > 0) {
        if (!user.tags || !quest.tags.some((tag) => user.tags.includes(tag))) {
          throw new BadRequestException(
            'You are not authorized to access this quest',
          );
        }
      }
    }

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: questId },
        status: PARTICIPANT_STATUS.COMPLETED,
      },
    });

    if (participant) {
      throw new BadRequestException('You have already completed this quest');
    }

    return {
      questions: quest.mcqQuestions.map((q) => ({
        id: q.id,
        question: q.question,
        options: q.options,
        difficulty: q.difficulty,
        ...(isHR && { correctAnswers: q.correctAnswers }),
      })),
    };
  }

  async acceptMCQQuestions(
    user: UserEntity,
    mcqs: any[],
    questId: string,
  ): Promise<boolean> {
    if (!mcqs || !Array.isArray(mcqs) || mcqs.length === 0) {
      throw new BadRequestException('No MCQ questions provided');
    }

    const id = parseInt(questId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid quest ID');
    }

    const quest = await this.questRepo.findOne({
      where: { id },
      relations: ['enterprise', 'creator'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    if (quest.enterprise.id !== user.enterprise.id) {
      throw new BadRequestException(
        'You do not have permission to modify this quest',
      );
    }

    try {
      const mcqEntities = mcqs.map((mcq) => {
        return this.mcqQuestionRepo.create({
          question: mcq.question,
          options: mcq.options || [],
          correctAnswers: mcq.correctAnswers || [0],
          difficulty: mcq.difficulty || 'intermediate',
          quest,
        });
      });

      await this.mcqQuestionRepo.save(mcqEntities);
      this.logger.log(
        `Saved ${mcqEntities.length} MCQ questions for quest ID ${questId}`,
      );

      return true;
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to accept MCQ questions: ${error.message}`,
        path: `/ai/quests/mcq/accept`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new InternalServerErrorException('Failed to save MCQ questions');
    }
  }

  async rejectMCQQuestions(
    user: UserEntity,
    questId: string,
  ): Promise<boolean> {
    const id = parseInt(questId, 10);
    if (isNaN(id)) {
      throw new BadRequestException('Invalid quest ID');
    }

    const quest = await this.questRepo.findOne({
      where: { id },
      relations: ['enterprise', 'creator', 'mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('Quest not found');
    }

    if (quest.enterprise.id !== user.enterprise.id) {
      throw new BadRequestException(
        'You do not have permission to modify this quest',
      );
    }

    try {
      if (quest.mcqQuestions && quest.mcqQuestions.length > 0) {
        await this.mcqQuestionRepo.remove(quest.mcqQuestions);
      }

      this.logger.log(
        `Rejected and removed MCQ questions for quest ID ${questId}`,
      );
      return true;
    } catch (error) {
      const errorResponse = {
        error: true,
        statusCode: 500,
        message: `Failed to reject MCQ questions: ${error.message}`,
        path: `/ai/quests/mcq/reject`,
        errorId: Date.now(),
        timestamp: new Date(),
      };
      this.logger.error(errorResponse);
      throw new InternalServerErrorException('Failed to reject MCQ questions');
    }
  }

  private parseEvaluationForResponse(
    questMetric: UserQuestMetricsEntity,
    submissionMediaType?: string,
  ): any {
    if (submissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT) {
      return questMetric.overallImprovementNeeded || '';
    }

    try {
      const parsed = JSON.parse(questMetric.overallAISuggestion);
      if (parsed && typeof parsed === 'object') {
        // Return the enhanced evaluation structure
        return {
          strength: parsed.strength || '',
          weakness: parsed.weakness || '',
          recommendation: parsed.recommendation || '',
        };
      }
    } catch (error) {}

    return questMetric.overallAISuggestion || '';
  }
}
