import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsBoolean,
  <PERSON><PERSON><PERSON>,
  IsNotEmpty,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import {
  QUEST_DIFFICULTY_TYPES,
  QUEST_MEDIA_TYPE,
  QUEST_SCOPE,
} from 'src/models/quest-entity';
import { Quest_ParticipantDTO } from '../quest-interaction/quest-interaction-dto';
import { QuestTypeDto } from './questType.dto';
import { ReportDTO } from 'src/reports/reports-dto';

export class Quest_MediaDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The url of the quest media',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  url: string;

  @ApiProperty({
    description: 'The quest media type of the quest',
    enum: QUEST_MEDIA_TYPE,
    example: 'video',
  })
  @IsEnum(QUEST_MEDIA_TYPE, {
    message: 'Quest Media Type must be one of the following: image, video',
  })
  @IsNotEmpty()
  type: QUEST_MEDIA_TYPE;

  @ApiProperty({
    description: 'The date when the quest media was uploaded',
    type: String,
    format: 'date-time',
  })
  @IsNotEmpty()
  uploadedAt: Date;

  @ApiProperty({
    description: 'Indicates if the quest media is deleted',
    example: 'false',
  })
  @IsBoolean()
  @IsNotEmpty()
  isDeleted: boolean;

  static transform(object: any): Quest_MediaDTO {
    const transformedObj = new Quest_MediaDTO();

    transformedObj.id = object.id;
    transformedObj.url = object.url;
    transformedObj.type = object.type;
    transformedObj.uploadedAt = object.uploadedAt;
    transformedObj.isDeleted = object.isDeleted;

    return transformedObj;
  }
}

export class Quest_EnterpriseDTO {
  @ApiProperty({
    description: 'The unique identifier of the enterprise',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The name of the enterprise quest belongs to.',
    example: 'ABCD',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  static transform(object: any): Quest_EnterpriseDTO {
    const transformedObj = new Quest_EnterpriseDTO();
    transformedObj.id = object.id;
    transformedObj.name = object.name;

    return transformedObj;
  }
}

export class Quest_CreatorDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest creator',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'The email of the quest creator.',
    example: '<EMAIL>',
  })
  @IsString()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'The firstName of the quest creator.',
    example: 'John',
  })
  @IsString()
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    description: 'The lastName of the quest creator.',
    example: 'Doe',
  })
  @IsString()
  @IsNotEmpty()
  lastName: string;

  @ApiProperty({
    description: 'The avatar of the quest creator.',
    example: 'url',
  })
  @IsString()
  @IsNotEmpty()
  avatar: string;

  static transform(object: any): Quest_CreatorDTO {
    const transformedObj = new Quest_CreatorDTO();
    transformedObj.id = object.id;
    transformedObj.email = object.email;
    transformedObj.firstName = object.firstName;
    transformedObj.lastName = object.lastName;
    transformedObj.avatar = object.avatar;

    return transformedObj;
  }
}

export class QuestDTO {
  @ApiProperty({
    description: 'The unique identifier of the quest',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  id: number;

  @ApiProperty({
    description: 'Indictaes that quest is completed by user or not...',
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isCompleted: boolean;

  @ApiProperty({
    description: 'The title of the quest',
    example: 'Quest for the Lost Artifact',
  })
  @IsString()
  @IsNotEmpty()
  title: string;

  @ApiProperty({
    description: 'A brief description of the quest',
    example: 'A thrilling adventure to find the lost artifact.',
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: 'The number of completion credits awarded for this quest',
    example: 100,
  })
  @IsNumber()
  @IsNotEmpty()
  completionCredits: number;

  @ApiProperty({
    description: 'The start date of the quest',
    type: String,
    format: 'date-time',
    example: '2024-10-22T10:00:00Z',
  })
  @IsString()
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({
    description: 'The end date of the quest',
    type: String,
    format: 'date-time',
    example: '2024-10-22T10:00:00Z',
  })
  @IsString()
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({
    description: 'The quest type of the quest',
    type: QuestTypeDto,
  })
  @IsNotEmpty()
  questType: QuestTypeDto;

  @ApiProperty({
    description: 'easy | intermediate | hard | very hard (optional)',
    enum: QUEST_DIFFICULTY_TYPES,
    example: 'easy | intermediate | hard | very hard (optional)',
    required: false,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES, {
    message:
      'Quest difficuty must be one of the following: easy ,intermediate ,hard or very hard',
  })
  @IsNotEmpty()
  difficulty: string;

  @ApiProperty({
    description: 'The date when the quest was created',
    type: String,
    format: 'date-time',
  })
  @IsNotEmpty()
  createdAt: Date;

  @ApiProperty({
    description: 'The date when the quest was last updated',
    type: String,
    format: 'date-time',
  })
  @IsNotEmpty()
  updatedAt: Date;

  @ApiProperty({
    description: 'Indicates if the quest is active',
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isActive: boolean;

  @ApiProperty({
    description: 'Indicates if the quest is deleted',
    example: false,
  })
  @IsBoolean()
  @IsNotEmpty()
  isDeleted: boolean;

  @ApiProperty({
    description: 'text | image | video (optional)',
    enum: QUEST_DIFFICULTY_TYPES,
    example: 'text | image | video  (optional)',
    required: false,
  })
  @IsEnum(QUEST_DIFFICULTY_TYPES, {
    message:
      'Quest submission type must be one of the following: text, image or video',
  })
  submissionMediaType: string;

  @ApiProperty({
    description: 'The user who created the quest',
    type: Quest_CreatorDTO,
  })
  @IsNotEmpty()
  creator: Quest_CreatorDTO;

  @ApiProperty({
    description: 'The enterprise associated with the quest',
    type: Quest_EnterpriseDTO,
  })
  @IsNotEmpty()
  enterprise: Quest_EnterpriseDTO;

  @ApiProperty({
    description: 'Array of Quest Media',
    type: [Quest_MediaDTO],
  })
  @IsNotEmpty()
  media: Quest_MediaDTO[];

  @ApiProperty({
    description: 'Array of quest participants',
    type: [Quest_ParticipantDTO],
  })
  @IsNotEmpty()
  participants: Quest_ParticipantDTO[];

  @ApiProperty({
    description: 'Array of quest reports',
    type: [ReportDTO],
  })
  @IsNotEmpty()
  reports: ReportDTO[];

  @ApiProperty({
    description: 'scope of quest',
    example: 'ai',
  })
  @IsNotEmpty()
  scope: QUEST_SCOPE;

  @ApiProperty({
    description: 'Custom name for custom quest types',
    example: 'My Custom Adventure Quest',
  })
  customQuestName?: string;

  static transform(
    object: any,
    showCompleted: boolean,
    isCompleted: boolean = false,
  ): QuestDTO {
    const transformedObj: QuestDTO = new QuestDTO(); 
    transformedObj.id = object.id;
    transformedObj.title = object.title; 
    transformedObj.description = object.description;
    transformedObj.completionCredits = object.completionCredits;
    transformedObj.startDate = object.startDate;
    transformedObj.endDate = object.endDate;
    transformedObj.difficulty = object.difficulty;
    transformedObj.createdAt = object.createdAt;
    transformedObj.updatedAt = object.updatedAt;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.submissionMediaType = object.submissionMediaType;
    transformedObj.isActive = object.isActive;
    transformedObj.scope = object.scope;
    transformedObj.customQuestName = object.customQuestName;

    if (showCompleted) {
      transformedObj.isCompleted = isCompleted;
    }

    // Map QuestType
    if (object.questType) {
      transformedObj.questType = QuestTypeDto.transform(object.questType);
    }

    // Map Creator
    if (object.creator) {
      transformedObj.creator = Quest_CreatorDTO.transform(object.creator);
    }

    // Map Enterprise
    if (object.enterprise) {
      transformedObj.enterprise = Quest_EnterpriseDTO.transform(
        object.enterprise,
      );
    }

    // Map Media
    if (object.media) {
      transformedObj.media = object.media
        .filter((item: any) => item.isDeleted === false)
        .map((mediaObject: any) => Quest_MediaDTO.transform(mediaObject));
    }

    // Map Participants
    if (object.participants) {
      transformedObj.participants = object.participants.map(
        (participantItem: any) =>
          Quest_ParticipantDTO.transform(participantItem),
      );
    }

    // Map Reports
    if (object.reports) {
      transformedObj.reports = object.reports.map((reportItem: any) =>
        ReportDTO.transform(reportItem),
      );
    }

    return transformedObj;
  }
}
