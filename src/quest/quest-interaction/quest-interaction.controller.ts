import { Api<PERSON><PERSON>erA<PERSON>, ApiResponse, ApiTags } from '@nestjs/swagger';
import { QuestInteractionService } from './quest-interaction.service';
import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  Post,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import { submitQuestReqDTO, SubmitQuestResDTO } from './quest-interaction-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { FileInterceptor } from '@nestjs/platform-express';
import { getMulterMediaOptions } from 'src/utils/multer.utils';
import { AllowedMixEntensions } from 'src/utils/allowedExtensions.utils';
import { GetAIQuestSubmissionMediaResDTO } from '../AI-quest/AI-quest-dto';
import { MCQSubmissionDTO } from '../AI-quest/AI-quest-dto/AI-mcq.dto';

@ApiTags('user-quests')
@ApiBearerAuth()
@Controller()
export class QuestInteractionController {
  constructor(
    private readonly questService: QuestInteractionService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Submit to enterprise Quest',
    type: SubmitQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('quest/submit/:questId')
  @UseInterceptors(
    FileInterceptor(
      'submission_media',
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedMixEntensions,
      }),
    ),
  )
  @UseGuards(AuthGuard)
  async SubmitToEnterpriseQuest(
    @Req() req: Request,
    @Param('questId') questId: string,
    @UploadedFile() submission_media: Express.Multer.File,
    @Body() completionData: submitQuestReqDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.questService.submitToEnterpriseQuest(
      user,
      questId,
      submission_media,
      completionData,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Get EP Quest Submission Medias',
    type: GetAIQuestSubmissionMediaResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('quest/media/:questId')
  @UseGuards(AuthGuard)
  async GetQuestSubmissionMedia(
    @Req() req: Request,
    @Param('questId') questId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.questService.getEpQuestSubmissionMedia(user, questId);
  }

  @Get('quest/mcq/:questId/questions')
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'Enterprise MCQ questions retrieved successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  async getEnterpriseMCQQuestions(
    @Req() req: Request,
    @Param('questId') questId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    const questIdNumber = this.questService.validateAndGetQuestId(questId);
    return await this.questService.getMCQQuestions(questIdNumber, user);
  }

  @Post('quest/mcq/:questId/submit')
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'Enterprise MCQ answers submitted successfully',
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request',
    type: ErrorResponse,
  })
  async submitEnterpriseMCQAnswers(
    @Req() req: Request,
    @Param('questId') questId: string,
    @Body() submission: MCQSubmissionDTO,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    const questIdNumber = this.questService.validateAndGetQuestId(questId);
    return await this.questService.submitMCQAnswers(
      questIdNumber,
      submission,
      user,
    );
  }
}
