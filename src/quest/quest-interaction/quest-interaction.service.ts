import { UserEntity } from 'src/models/user-entity';
import { submitQuestReqDTO, SubmitQuestResDTO } from './quest-interaction-dto';
import { BadRequestException, Injectable } from '@nestjs/common';
import {
  PARTICIPANT_STATUS,
  QuestCompletionProofMediaEntity,
  QuestEntity,
  QuestParticipantEntity,
  SUBMISSION_MEDIA_TYPES,
} from 'src/models/quest-entity';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, EntityManager, Repository } from 'typeorm';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { UserCreditsDTO } from '../AI-quest/AI-quest-dto/UserCredits.dto';
import {
  AllowedImageExtensions,
  AllowedVideoExtensions,
} from 'src/utils/allowedExtensions.utils';
import { QUEST_SCOPE } from 'src/models/quest-entity/quest.entity';
import { AIQuestService } from '../AI-quest/AI-quest.service';
import { GetAIQuestSubmissionMediaResDTO } from '../AI-quest/AI-quest-dto';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { MCQSubmissionDTO } from '../AI-quest/AI-quest-dto/AI-mcq.dto';
import { UserQuestMetricsEntity } from 'src/models/metrics-entity';

@Injectable()
export class QuestInteractionService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly s3Service: S3Service,
    private readonly aiQuestService: AIQuestService,
    private readonly leaderboardService: LeaderboardService,

    @InjectRepository(QuestEntity)
    private readonly questRepo: Repository<QuestEntity>,

    @InjectRepository(QuestParticipantEntity)
    private readonly participantRepo: Repository<QuestParticipantEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(QuestCompletionProofMediaEntity)
    private readonly questCompletionProofMediaRepo: Repository<QuestCompletionProofMediaEntity>,

    @InjectRepository(MCQQuestionEntity)
    private readonly mcqQuestionRepo: Repository<MCQQuestionEntity>,

    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,

    @InjectRepository(UserQuestMetricsEntity)
    private readonly userQuestMetricsRepo: Repository<UserQuestMetricsEntity>,
  ) {}

  validateAndGetQuestId(questId: string): number {
    const id = parseInt(questId, 10);

    if (isNaN(id)) {
      throw new BadRequestException(`Invalid Quest id provided ${id}.`);
    }

    return id;
  }

  async submitToEnterpriseQuest(
    user: UserEntity,
    questId: string,
    submissionMedia: Express.Multer.File,
    completionData: submitQuestReqDTO,
  ): Promise<SubmitQuestResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        id,
      },
      relations: [
        'enterprise',
        'participants',
        'participants.user',
        'questType',
      ],
    });

    if (!quest || quest.isDeleted === true) {
      throw new BadRequestException('Quest not found !!');
    }

    if (!quest.isActive) {
      throw new BadRequestException(
        'Quest is not live right now, come back later...',
      );
    }

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: quest.id },
      },
    });

    let userCredit: UserCreditsEntity;
    let msg = '';

    if (participant) {
      if (participant.status === PARTICIPANT_STATUS.COMPLETED) {
        throw new BadRequestException('You already completed this quest...');
      } else {
        // submit to the EP quest.
        userCredit = await this.questSubmitTransaction(
          submissionMedia,
          quest,
          completionData,
          participant,
          user,
        );

        msg = 'quest submitted successfully !!';
      }
    } else {
      // create a participant entity with this user for this quest.
      const new_participant = this.participantRepo.create({
        quest,
        user,
        status: PARTICIPANT_STATUS.PENDING,
        enterprise: { id: user.enterprise.id },
      });

      quest.participants.push(new_participant);

      await this.participantRepo.save(new_participant);
      await this.userRepo.save(user);
      await this.questRepo.save(quest);

      // then submit to the quest.
      userCredit = await this.questSubmitTransaction(
        submissionMedia,
        quest,
        completionData,
        new_participant,
        user,
      );

      msg = 'participated in quest and submitted the quest successfully !!';
    }

    if (userCredit.credits > 0) {
      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        quest.completionCredits,
        userCredit,
      );
    }

    return {
      error: false,
      msg,
      userCredit: UserCreditsDTO.transform(userCredit),
    };
  }

  private async questSubmitTransaction(
    submissionMedia: Express.Multer.File,
    quest: QuestEntity,
    completionData: submitQuestReqDTO,
    participant: QuestParticipantEntity,
    user: UserEntity,
  ): Promise<UserCreditsEntity> {
    return await this.dataSource.transaction(async (manager) => {
      const mediaType = this.determineAndValidateMedia(
        submissionMedia,
        quest.submissionMediaType,
      );
      const { caption, completeDate } = completionData;

      let new_completion_Proof_Media = new QuestCompletionProofMediaEntity();
      new_completion_Proof_Media.caption = caption;

      if (mediaType !== SUBMISSION_MEDIA_TYPES.TEXT) {
        const uploadedFile = await this.s3Service.uploadFile(submissionMedia);
        new_completion_Proof_Media.url = uploadedFile.Location;
      }

      new_completion_Proof_Media = await manager.save(
        new_completion_Proof_Media,
      );

      participant.questCompletionProof = [new_completion_Proof_Media];
      participant.status = PARTICIPANT_STATUS.COMPLETED;

      await manager.save(participant);

      return this.aiQuestService.createUserCredit(
        manager,
        user,
        quest,
        completeDate,
        completionData.caption,
      );
    });
  }

  private determineMediaType(fileExt: string): string {
    let providedSubmissionMedia: string;
    if (AllowedVideoExtensions.includes(fileExt)) {
      providedSubmissionMedia = SUBMISSION_MEDIA_TYPES.VIDEO;
    } else if (AllowedImageExtensions.includes(fileExt)) {
      providedSubmissionMedia = SUBMISSION_MEDIA_TYPES.IMAGE;
    } else {
      providedSubmissionMedia = SUBMISSION_MEDIA_TYPES.TEXT;
    }

    return providedSubmissionMedia;
  }

  private determineAndValidateMedia(
    submissionMedia: Express.Multer.File,
    expectedType: string,
  ): string {
    const fileExt = submissionMedia?.mimetype?.split('/')[1] || '';
    const mediaType = this.determineMediaType(fileExt);

    if (mediaType !== expectedType) {
      throw new BadRequestException(
        `Please provide quest completion proof of type ${expectedType}.`,
      );
    }

    return mediaType;
  }

  async getEpQuestSubmissionMedia(
    user: UserEntity,
    questId: string,
  ): Promise<GetAIQuestSubmissionMediaResDTO> {
    const id = this.validateAndGetQuestId(questId);

    const quest = await this.questRepo.findOne({
      where: {
        enterprise: { id: user.enterprise?.id || 0 },
        scope: QUEST_SCOPE.ENTERPRISE,
        id,
      },
    });

    if (!quest?.id || quest?.isDeleted === true) {
      throw new BadRequestException('Quest not found !!');
    }

    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: quest.id },
      },
      relations: ['questCompletionProof', 'quest', 'user'],
    });

    if (!participant) {
      throw new BadRequestException('Participant not found');
    }

    const completionMedias = participant.questCompletionProof || []; // Ensure it's never undefined

    const questMetric = await this.userQuestMetricsRepo.findOne({
      where: {
        quest: { id: parseInt(questId, 10) },
        user: { id: user.id },
      },
    });

    const defaultMetrics = {
      id: null,
      overallAnalysisScore: 0,
      credits: 0,
      createdAt: new Date(),
      evaluation: '',
    };

    if (!completionMedias || completionMedias.length <= 0) {
      return {
        error: false,
        caption: '',
        media_urls: [],
        metrics: questMetric
          ? {
              id: questMetric.id,
              overallAnalysisScore: questMetric.overallAnalysisScore,
              credits: questMetric.credits,
              createdAt: questMetric.createdAt,
              evaluation: this.parseEvaluationForResponse(
                questMetric,
                quest.submissionMediaType,
              ),
            }
          : defaultMetrics,
      };
    }

    return {
      error: false,
      caption: completionMedias[0]?.caption || '',
      media_urls:
        quest.submissionMediaType !== SUBMISSION_MEDIA_TYPES.TEXT
          ? completionMedias.map((item) => item.url)
          : [],
      metrics: questMetric
        ? {
            id: questMetric.id,
            overallAnalysisScore: questMetric.overallAnalysisScore,
            credits: questMetric.credits,
            createdAt: questMetric.createdAt,
            evaluation: this.parseEvaluationForResponse(
              questMetric,
              quest.submissionMediaType,
            ),
          }
        : defaultMetrics,
    };
  }

  private parseEvaluationForResponse(
    questMetric: UserQuestMetricsEntity,
    submissionMediaType?: string,
  ): any {
    if (!questMetric) return '';

    // For text-based quests, return the overallImprovementNeeded field
    if (submissionMediaType === SUBMISSION_MEDIA_TYPES.TEXT) {
      return questMetric.overallImprovementNeeded || '';
    }

    // For MCQ and other types, try to parse as JSON for structured format
    if (questMetric.overallAISuggestion) {
      try {
        const parsed = JSON.parse(questMetric.overallAISuggestion);
        if (parsed && typeof parsed === 'object') {
          return {
            strength: parsed.strength || '',
            weakness: parsed.weakness || '',
            recommendation: parsed.recommendation || '',
          };
        }
      } catch (error) {}
    }

    // If it's not JSON or doesn't have the expected structure, return the AI suggestion as fallback
    return questMetric.overallAISuggestion || '';
  }

  async getMCQQuestions(questId: number, user: UserEntity) {
    const quest = await this.questRepo.findOne({
      where: {
        id: questId,
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      },
      relations: ['mcqQuestions'],
    });

    if (!quest) {
      throw new BadRequestException('Enterprise MCQ quest not found');
    }

    // Check if user has already completed this quest
    const participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: questId },
        status: PARTICIPANT_STATUS.COMPLETED,
      },
    });

    if (participant) {
      throw new BadRequestException('You have already completed this quest');
    }

    return {
      questions: quest.mcqQuestions.map((q) => ({
        id: q.id,
        question: q.question,
        options: q.options,
        difficulty: q.difficulty,
      })),
    };
  }

  async submitMCQAnswers(
    questId: number,
    submission: MCQSubmissionDTO,
    user: UserEntity,
  ) {
    const quest = await this.questRepo.findOne({
      where: {
        id: questId,
        enterprise: { id: user.enterprise.id },
        scope: QUEST_SCOPE.ENTERPRISE,
        submissionMediaType: SUBMISSION_MEDIA_TYPES.MCQ,
      },
      relations: ['mcqQuestions', 'enterprise', 'questType'],
    });

    if (!quest) {
      throw new BadRequestException('Enterprise MCQ quest not found');
    }

    if (!quest.isActive) {
      throw new BadRequestException('Quest is not currently active');
    }

    let participant = await this.participantRepo.findOne({
      where: {
        user: { id: user.id },
        quest: { id: questId },
      },
    });

    if (participant && participant.status === PARTICIPANT_STATUS.COMPLETED) {
      throw new BadRequestException('You have already completed this quest');
    }

    // Create participant if doesn't exist
    if (!participant) {
      participant = this.participantRepo.create({
        user: user,
        quest: quest,
        status: PARTICIPANT_STATUS.PENDING,
        enterprise: { id: user.enterprise.id },
      });
      await this.participantRepo.save(participant);
    }

    let correctAnswers = 0;
    let totalQuestions = quest.mcqQuestions.length;
    const questionsWithAnswers = [];

    for (const answer of submission.answers) {
      const question = await this.mcqQuestionRepo.findOne({
        where: { id: answer.questionId },
      });

      if (!question) {
        throw new BadRequestException(
          `Question with ID ${answer.questionId} not found`,
        );
      }

      const invalidOptions = answer.selectedOptions.filter(
        (opt) => opt < 0 || opt >= question.options.length,
      );
      if (invalidOptions.length > 0) {
        throw new BadRequestException(
          `Invalid option indices: ${invalidOptions.join(', ')}`,
        );
      }

      const isCorrect =
        answer.selectedOptions.length === question.correctAnswers.length &&
        answer.selectedOptions.every((opt) =>
          question.correctAnswers.includes(opt),
        );

      if (isCorrect) {
        correctAnswers++;
      }

      questionsWithAnswers.push({
        question: question.question,
        options: question.options,
        correctAnswers: question.correctAnswers,
        userAnswers: answer.selectedOptions,
        isCorrect: isCorrect,
      });
    }

    const score = (correctAnswers / totalQuestions) * 100;

    participant.status = PARTICIPANT_STATUS.COMPLETED;
    await this.participantRepo.save(participant);

    const userCredit = await this.aiQuestService.createUserCreditWithMCQDetails(
      this.dataSource.manager,
      user,
      quest,
      new Date().toISOString(),
      { answers: submission.answers } as MCQSubmissionDTO,
      questionsWithAnswers,
      score,
    );

    try {
      const existingMetrics = await this.userQuestMetricsRepo.findOne({
        where: {
          user: { id: user.id },
          quest: { id: questId },
        },
      });

      if (existingMetrics) {
        existingMetrics.answer = JSON.stringify(submission);
        await this.userQuestMetricsRepo.save(existingMetrics);
      }
    } catch (error) {}

    if (userCredit.credits > 0) {
      await this.leaderboardService.updateLeaderboards(
        user,
        quest.questType,
        userCredit.credits,
        userCredit,
      );
    }

    return {
      score,
      correctAnswers,
      totalQuestions,
      passed: score >= 70,
      userCredit: UserCreditsDTO.transform(userCredit),
    };
  }
}
