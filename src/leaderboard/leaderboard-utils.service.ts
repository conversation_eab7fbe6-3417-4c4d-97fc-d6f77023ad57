import { BadRequestException, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, Not } from 'typeorm';
import {
  LEADERBOARD_TIME_PERIODS,
  LeaderboardEntity,
} from '../models/leaderboard-entity';
import { UserEntity } from '../models/user-entity';
import { UserCreditsEntity } from '../models/credits-entity';
import { QuestTypesEntity } from '../models/quest-entity';
import { GetLeaderBoardResDTO } from './leaderboard-dto';
import * as moment from 'moment';
import { CreateLeaderboardEntryPropInterface } from './interfaces';

@Injectable()
export class LeaderboardUtilsService {
  constructor(
    private readonly dataSource: DataSource,

    @InjectRepository(LeaderboardEntity)
    private leaderboardRepo: Repository<LeaderboardEntity>,

    @InjectRepository(QuestTypesEntity)
    private questTypeRepo: Repository<QuestTypesEntity>,
  ) {}

  validateDateFormat(date: string): void {
    const format = 'YYYY-MM-DDTHH:mm:ss.SSS';

    const isValid = moment(date, format, true).isValid();

    if (!isValid) {
      throw new BadRequestException('Invalid date format provided !!');
    }
  }

  buildWhereConditionForGetRoutes = (
    user: UserEntity,
    questTypeId: number | null,
    filter: string,
    userDate: string,
    isOverall: boolean = false,
  ): any => {
    const date = userDate.split('T')[0];
    const fullDate = userDate;

    let whereCondition: any = {
      enterprise: { id: user.enterprise.id },
      user: { isDeleted: false },
      isOverall,
    };

    if (questTypeId) {
      whereCondition.questType = { id: questTypeId };
    }

    if (filter === LEADERBOARD_TIME_PERIODS.DAILY) {
      whereCondition = {
        ...whereCondition,
        timePeriod: LEADERBOARD_TIME_PERIODS.DAILY,
        date,
      };
    } else if (filter === LEADERBOARD_TIME_PERIODS.MONTHLY) {
      whereCondition = {
        ...whereCondition,
        timePeriod: LEADERBOARD_TIME_PERIODS.MONTHLY,
        month: moment(fullDate).month(),
        year: moment(fullDate).year(),
      };
    }

    return whereCondition;
  };

  validateFilterData = (
    userDate: string,
    filter: string,
    questTypeId?: number,
  ): void => {
    if (!userDate) {
      throw new BadRequestException('Please provide userDate.');
    }
    this.validateDateFormat(userDate);

    if (!filter) {
      throw new BadRequestException('Please provide filter.');
    }

    if (questTypeId === undefined) {
      throw new BadRequestException('Please provide the quest type id.');
    }
  };

  fetchLeaderboard = async (
    leaderboardRepo: Repository<LeaderboardEntity>,
    whereCondition: any,
    limit: number | string,
    pageNumber: number = 1,
  ): Promise<GetLeaderBoardResDTO> => {
    const set_limit = isNaN(Number(limit)) ? 10 : Number(limit);
    const offset = (pageNumber - 1) * set_limit;

    const [leaderboard, total] = await leaderboardRepo.findAndCount({
      where: whereCondition,
      relations: ['questType', 'user'],
      order: {
        totalCredits: 'DESC',
        fullDate: 'ASC',
      },
      skip: offset,
      take: set_limit,
    });

    return { error: false, total, nbHits: leaderboard.length, leaderboard };
  };

  createLeaderboardEntry({
    user,
    timePeriod,
    date,
    fullDate,
    month,
    year,
    isOverall = false,
    questType = null,
  }: CreateLeaderboardEntryPropInterface) {
    const entry = new LeaderboardEntity();
    entry.enterprise = user.enterprise;
    entry.isOverall = isOverall;
    entry.user = user;
    entry.timePeriod = timePeriod;
    entry.date = date;
    entry.fullDate = fullDate;
    entry.month = month;
    entry.year = year;

    if (questType) entry.questType = questType;

    return entry;
  }

  async createOrUpdateDailyLeaderboards(
    user: UserEntity,
    date: string,
    fullDate: string,
    month: number,
    year: number,
    questTypes: QuestTypesEntity[],
  ) {
    try {
      await this.dataSource.transaction(async (manager) => {
        const existingDailyLeaderboard = await manager.findOne(
          LeaderboardEntity,
          {
            where: {
              user: { id: user.id },
              date,
              timePeriod: LEADERBOARD_TIME_PERIODS.DAILY,
            },
          },
        );

        if (!existingDailyLeaderboard) {
          const dailyOverallLeaderboard = this.createLeaderboardEntry({
            user,
            timePeriod: LEADERBOARD_TIME_PERIODS.DAILY,
            date,
            fullDate,
            month,
            year,
            isOverall: true,
          });

          await manager.save(dailyOverallLeaderboard);

          for (const questType of questTypes) {
            const questLeaderboard = this.createLeaderboardEntry({
              user,
              timePeriod: LEADERBOARD_TIME_PERIODS.DAILY,
              date,
              fullDate,
              month,
              year,
              questType,
            });

            await manager.save(questLeaderboard);
          }
        }
      });
    } catch (error) {
      throw error;
    }
  }

  async createOrUpdateMonthlyLeaderboards(
    user: UserEntity,
    date: string,
    fullDate: string,
    month: number,
    year: number,
    questTypes: QuestTypesEntity[],
  ) {
    try {
      await this.dataSource.transaction(async (manager) => {
        const existingMonthlyLeaderboard = await manager.findOne(
          LeaderboardEntity,
          {
            where: {
              user: { id: user.id },
              month,
              year,
              timePeriod: LEADERBOARD_TIME_PERIODS.MONTHLY,
            },
          },
        );

        if (!existingMonthlyLeaderboard) {
          const monthlyOverallLeaderboard = this.createLeaderboardEntry({
            user,
            timePeriod: LEADERBOARD_TIME_PERIODS.MONTHLY,
            date,
            fullDate,
            month,
            year,
            isOverall: true,
          });

          await manager.save(monthlyOverallLeaderboard);

          for (const questType of questTypes) {
            const questLeaderboard = this.createLeaderboardEntry({
              user,
              timePeriod: LEADERBOARD_TIME_PERIODS.MONTHLY,
              date,
              fullDate,
              month,
              year,
              questType,
            });

            await manager.save(questLeaderboard);
          }
        }
      });
    } catch (error) {
      throw error;
    }
  }

  buildWhereConditionForUpdateService(
    user: UserEntity,
    questType: QuestTypesEntity,
    timePeriod: LEADERBOARD_TIME_PERIODS,
    userCredit: UserCreditsEntity,
  ) {
    const baseCondition = {
      enterprise: { id: user.enterprise.id },
      user: { id: user.id, isDeleted: false },
      timePeriod,
      questType: { id: questType.id },
    };

    if (timePeriod === LEADERBOARD_TIME_PERIODS.DAILY) {
      return {
        ...baseCondition,
        date: userCredit.submissionDate.split('T')[0],
      };
    }

    if (timePeriod === LEADERBOARD_TIME_PERIODS.MONTHLY) {
      return {
        ...baseCondition,
        month: moment(userCredit.submissionFullDate).month(),
        year: moment(userCredit.submissionFullDate).year(),
      };
    }

    return baseCondition;
  }

  async updateUserOverallLeaderboardEntry(
    user: UserEntity,
    timePeriod: LEADERBOARD_TIME_PERIODS,
    userDate: string,
  ) {
    const date = userDate.split('T')[0];
    const fullDate = userDate;
    const month = moment(fullDate).month();
    const year = moment(fullDate).year();

    try {
      await this.dataSource.transaction(async (manager) => {
        let baseCondition: any = {
          enterprise: { id: user.enterprise.id },
          user: { id: user.id },
          timePeriod,
        };

        if (timePeriod === LEADERBOARD_TIME_PERIODS.DAILY) {
          baseCondition = { ...baseCondition, date: date };
        }

        if (timePeriod === LEADERBOARD_TIME_PERIODS.MONTHLY) {
          baseCondition = {
            ...baseCondition,
            month: moment(fullDate).month(),
            year: moment(fullDate).year(),
          };
        }

        const leaderboardEntries = await manager.find(LeaderboardEntity, {
          where: {
            ...baseCondition,
            isOverall: false,
          },
        });

        const overallLeaderboardEntry = await manager.findOne(
          LeaderboardEntity,
          {
            where: {
              ...baseCondition,
              isOverall: true,
            },
          },
        );

        if (!leaderboardEntries?.length || !overallLeaderboardEntry) {
          throw new BadRequestException(
            `Not able to fetch user's leaderboards!`,
          );
        }

        overallLeaderboardEntry.totalCredits = leaderboardEntries.reduce(
          (total, entry) => total + entry.totalCredits,
          0,
        );

        await manager.save(overallLeaderboardEntry);
      });
    } catch (error) {
      throw error;
    }
  }

  async createUserLeaderboards(
    user: UserEntity,
    userDate: string,
    timePeriod: LEADERBOARD_TIME_PERIODS,
  ) {
    const date = userDate.split('T')[0];
    const fullDate = userDate;
    const month = moment(fullDate).month();
    const year = moment(fullDate).year();

    // Include all quest types
    const questTypes = await this.questTypeRepo.find();

    if (timePeriod === LEADERBOARD_TIME_PERIODS.DAILY) {
      await this.createOrUpdateDailyLeaderboards(
        user,
        date,
        fullDate,
        month,
        year,
        questTypes,
      );
    }

    if (timePeriod === LEADERBOARD_TIME_PERIODS.MONTHLY) {
      await this.createOrUpdateMonthlyLeaderboards(
        user,
        date,
        fullDate,
        month,
        year,
        questTypes,
      );
    }
  }

  async updateUserQuestTypeLeaderboardEntry(
    user: UserEntity,
    questType: QuestTypesEntity,
    timePeriod: LEADERBOARD_TIME_PERIODS,
    creditsIncrement: number,
    userCredit: UserCreditsEntity,
  ) {
    const whereCondition = this.buildWhereConditionForUpdateService(
      user,
      questType,
      timePeriod,
      userCredit,
    );

    let leaderboardEntry = await this.leaderboardRepo.findOne({
      where: whereCondition,
    });

    if (!leaderboardEntry) {
      await this.createUserLeaderboards(
        user,
        userCredit.submissionFullDate,
        timePeriod,
      );
    }

    leaderboardEntry = await this.leaderboardRepo.findOne({
      where: whereCondition,
    });

    if (!leaderboardEntry) {
      // Create the leaderboard entry directly if still missing
      const date = userCredit.submissionDate.split('T')[0];
      const fullDate = userCredit.submissionFullDate;
      const month = moment(fullDate).month();
      const year = moment(fullDate).year();

      const entry = this.createLeaderboardEntry({
        user,
        timePeriod,
        date,
        fullDate,
        month,
        year,
        questType,
      });
      await this.leaderboardRepo.save(entry);
      // Fetch again
      leaderboardEntry = await this.leaderboardRepo.findOne({
        where: whereCondition,
      });
      if (!leaderboardEntry) {
        throw new BadRequestException(
          `Failed to create or find leaderboard entry for user ${user.id} and quest type ${questType.id} (even after direct creation)`,
        );
      }
    }

    leaderboardEntry.totalCredits += creditsIncrement;
    await this.leaderboardRepo.save(leaderboardEntry);

    await this.updateUserOverallLeaderboardEntry(
      user,
      timePeriod,
      userCredit.submissionFullDate,
    );
  }
}
