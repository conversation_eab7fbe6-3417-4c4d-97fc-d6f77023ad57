import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { EnterpriseEntity, UserEntity } from 'src/models/user-entity';
import { ROLE_VALUES, RoleEntity } from 'src/models/user-entity/role.entity';
import {
  AccessEnterpriseAsHRResDTO,
  AdminUserDTO,
  ExitEnterpriseAsHRResDTO,
  GetAdminPermissionsRouteResDTO,
} from './admin-permissions-dto';
import { AdminUtilsService } from '../admin-utils.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

@Injectable()
export class AdminPermissionsService {
  constructor(
    private readonly adminUtilsService: AdminUtilsService,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,
  ) {}

  /**
   * Helper method to fetch updated user with all necessary relations
   * This ensures we always have fresh user data after DB changes
   */
  private async fetchUpdatedUserWithRelations(userId: number): Promise<UserEntity> {
    return await this.userRepo.findOne({
      where: { id: userId },
      relations: [
        'enterprise',
        'roles',
        'roles.permissions',
        'selectedQuestTypes',
        'department',
      ],
    });
  }

  // get Admin permissions route
  async getAdminPermissionsRoute(
    user: UserEntity,
  ): Promise<GetAdminPermissionsRouteResDTO> {
    const isAdminUser = user.roles.some(
      (item) => item.value === ROLE_VALUES.ADMIN,
    );

    if (!isAdminUser) {
      throw new BadRequestException(
        'Your account do not have the necessary permissions to access this resource.',
      );
    }

    const userProfile = AdminUserDTO.transform(user);

    return {
      error: false,
      permissions: userProfile.permissions,
    };
  }

  // Admin access any enterprise as HR
  async accessEnterpriseAsHR(
    user: UserEntity,
    enterpriseId: string,
  ): Promise<AccessEnterpriseAsHRResDTO> {
    const id = this.adminUtilsService.validateAndGetId(enterpriseId);

    const enterprise = await this.enterpriseRepo.findOne({
      where: { id, isDeleted: false },
    });

    if (!enterprise) {
      throw new BadRequestException('Enterprise with this ID does not exist.');
    }

    const isUserHR = user.roles.some((item) => item.value === ROLE_VALUES.HR);
    const HRrole = await this.roleRepo.findOne({
      where: { value: ROLE_VALUES.HR },
    });

    if (!HRrole) {
      throw new BadRequestException('HR Role not found in the system.');
    }

    // Check if the user is already logged into an enterprise
    if (user?.enterprise?.id) {
      if (isUserHR) {
        throw new UnauthorizedException(
          'Please log out from the current enterprise first.',
        );      } else {
        user.roles.push(HRrole);
        user = await this.userRepo.save(user);
        
        // Fetch the updated user with all relations
        const updatedUser = await this.fetchUpdatedUserWithRelations(user.id);
        
        const userResp = UserProfileDto.transform(updatedUser);
        return {
          error: false,
          msg: 'HR role added to access enterprise.',
          user: userResp,
        };
      }
    }

    // If user is HR but not assigned to any enterprise, assign them now
    if (isUserHR && !user.enterprise?.id) {
      const HRRoleIndex = user.roles.findIndex(
        (item) => item.value === ROLE_VALUES.HR,
      );
      if (HRRoleIndex !== -1) user.roles.splice(HRRoleIndex, 1);
    }    user.enterprise = enterprise;
    user.roles.push(HRrole);
    await this.userRepo.save(user);

    // Fetch the updated user with all relations to ensure we have fresh data
    const updatedUser = await this.fetchUpdatedUserWithRelations(user.id);

    const userResp = UserProfileDto.transform(updatedUser);

    return {
      error: false,
      msg: 'Logged into the enterprise successfully!',
      user: userResp,
    };
  }

  // Admin exit any enerprise as HR
  async exitEnterpriseAsHR(
    user: UserEntity,
  ): Promise<ExitEnterpriseAsHRResDTO> {
    const isUserHR = user.roles.some((item) => item.value === ROLE_VALUES.HR);
    let msg = '';

    if (isUserHR) {
      const HRrole = await this.roleRepo.findOne({
        where: { value: ROLE_VALUES.HR },
      });

      if (!HRrole) {
        throw new BadRequestException('HR Role not found in the system.');
      }

      const HRRoleIndex = user.roles.findIndex(
        (item) => item.value === ROLE_VALUES.HR,
      );

      if (!user.enterprise?.id) {
        // Remove HR role from user
        if (HRRoleIndex !== -1) {
          user.roles.splice(HRRoleIndex, 1);
          msg = 'Removed HR role from you.';
        }
      } else {
        // Remove role and enterprise from user
        if (HRRoleIndex !== -1) {
          user.roles.splice(HRRoleIndex, 1);
        }
        user.enterprise = null;
        msg = 'Successfully exited from enterprise.';
      }
    } else {
      if (!user.enterprise?.id) {
        // Already not in any enterprise
        throw new BadRequestException('You are not part of any enterprise.');
      }
      // Remove enterprise ID from user
      user.enterprise = null;
      msg = 'Removed enterprise from you.';
    }    user = await this.userRepo.save(user);
    
    // Fetch the updated user with all relations to ensure fresh data
    const updatedUser = await this.fetchUpdatedUserWithRelations(user.id);
    
    const userResp = UserProfileDto.transform(updatedUser);

    return {
      error: false,
      msg,
      user: userResp,
    };
  }
}
