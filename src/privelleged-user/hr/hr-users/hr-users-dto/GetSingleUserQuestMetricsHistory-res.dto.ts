import { ApiProperty } from '@nestjs/swagger';
import { IsN<PERSON>ber, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { UserCreditsDTO } from 'src/quest/AI-quest/AI-quest-dto/UserCredits.dto';

export class GetSingleUserQuestMetricsHistoryResDTO extends BaseResponse {
  @ApiProperty({ description: 'Number of hits' })
  @IsNumber()
  nbHits: number;

  @ApiProperty({ description: 'Total number of metrics' })
  @IsNumber()
  total: number;

  @ApiProperty({
    description: 'Array of user credit metrics',
    type: [UserCreditsDTO],
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UserCreditsDTO)
  metricsHistory: UserCreditsDTO[];
}
