import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  Req,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiConsumes,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import {
  AddBulkUsersResDTO,
  UpdateUserResDTO,
  AddSingleUserReqDTO,
  AddSingleUserResDTO,
  GetAllUsersResDTO,
  UpdateUserReqDTO,
  GetSingleUserQuestMetricsHistoryResDTO,
} from './hr-users-dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Request } from 'express';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { HrUsersService } from './hr-users.service';
import { FileInterceptor } from '@nestjs/platform-express';
import { getMulterCSVOptions } from 'src/utils/multer.utils';
import {
  GetAllUsersFilterQueryInterface,
  GetSingleUserQuestMetricsHistoryFilterQueryInterface,
} from './interfaces';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';

@ApiTags('HR-users')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/users')
export class HrUsersController {
  constructor(
    private readonly hrUsersService: HrUsersService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 201,
    description: 'HR Delete User',
    type: UpdateUserResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('DELETE_USER')
  @Delete('/delete/:userId')
  async DeleteUser(@Req() req: Request, @Param('userId') userId: string) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrUsersService.deleteUser(userId, user);
  }

  @ApiResponse({
    status: 201,
    description: 'HR Update User',
    type: UpdateUserResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('UPDATE_USER')
  @Put('/update/:userId')
  async UpdateUserStatus(
    @Body() body: UpdateUserReqDTO,
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrUsersService.updateUser(body, user, userId);
  }

  @ApiResponse({
    status: 201,
    description: 'HR Add New User',
    type: AddSingleUserResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('ADD_SINGLE_USER')
  @Post('/add/single')
  async AddSingleUser(@Body() body: AddSingleUserReqDTO, @Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrUsersService.addSingleUser(body, user);
  }

  @ApiResponse({
    status: 201,
    description: 'HR Add New User via CSV file',
    type: AddBulkUsersResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'CSV file containing user data for bulk upload',
    required: true,
    schema: {
      type: 'object',
      properties: {
        csvFile: {
          type: 'string',
          format: 'binary', // This specifies a file upload in Swagger
        },
      },
    },
  })
  @Authority('ADD_BULK_USERS')
  @UseInterceptors(
    FileInterceptor('csvFile', getMulterCSVOptions({ fileSize: 50 })),
  )
  @Post('/add/bulk')
  async AddBulkUser(
    @UploadedFile() csvFile: Express.Multer.File,
    @Req() req: Request,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrUsersService.addBulkUsers(csvFile, user);
  }

  @ApiResponse({
    status: 201,
    description: 'HR Get All Users list',
    type: GetAllUsersResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the users list.',
    type: String,
  })
  @ApiQuery({
    name: 'name',
    required: false,
    description: 'The name filter for the users list.',
    type: String,
  })
  @Authority('GET_ALL_USERS_LIST')
  @Get()
  async GetAllUsers(
    @Req() req: Request,
    @Query() query: GetAllUsersFilterQueryInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrUsersService.getAllUsers(user, query);
  }

  @ApiResponse({
    status: 201,
    description: 'HR Get Single User Profile',
    type: UserProfileDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('GET_SINGLE_USER_PROFILE')
  @Get('/:userId')
  async GetSingleUserProfile(
    @Req() req: Request,
    @Param('userId') userId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrUsersService.getSingleUserProfile(user, userId);
  }

  @ApiResponse({
    status: 201,
    description: 'HR Get Single User Quest Metrics History',
    type: GetSingleUserQuestMetricsHistoryResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Authority('GET_SINGLE_USER_QUEST_METRICS_HISTORY')
  @Get('quest_metrics/:userId')
  async GetSingleUserQuestMetricsHistory(
    @Req() req: Request,
    @Param('userId') userId: string,
    @Query() queryFilters: GetSingleUserQuestMetricsHistoryFilterQueryInterface,
  ) {
    const user = this.userProfileService.getUserFromToken(req);

    return this.hrUsersService.getSingleUserQuestMetricsHistory(
      user,
      userId,
      queryFilters,
    );
  }
}
