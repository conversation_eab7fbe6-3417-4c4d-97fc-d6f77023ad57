import { BadRequestException, Injectable } from '@nestjs/common';
import {
  AddBulkUsersResDTO,
  AddSingleUserReqDTO,
  AddSingleUserResDTO,
  GetAllUsersResDTO,
  GetSingleUserProfileResDTO,
  GetSingleUserQuestMetricsHistoryResDTO,
  HRUserListDetailsDTO,
  UpdateUserReqDTO,
  UpdateUserResDTO,
} from './hr-users-dto';
import {
  AccessTokenEntity,
  ENTERPRISE_DOMAIN_STATUS,
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  RoleEntity,
  USER_DISABILTY_TYPES,
  UserEntity,
} from 'src/models/user-entity';
import { Like, Not, Raw, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import * as argon2 from 'argon2';
import { UserEnterpriseService } from 'src/user/user-enterprise-service/user-enterprise.service';
import { EmailService } from 'src/common/email.service';
import { ConfigService } from 'src/configuration/config.service';
import { UserProfileDto } from 'src/user/user-profile/user-profile-dto';
import * as stream from 'stream';
import * as csvParser from 'csv-parser';
import {
  CreateUserResInterface,
  GetAllUsersFilterQueryInterface,
  GetSingleUserQuestMetricsHistoryFilterQueryInterface,
} from './interfaces';
import { Parser } from 'json2csv';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { v4 as uuid } from 'uuid';
import { HrUtilsService } from '../hr-utils.service';
import { ROLE_VALUES } from 'src/models/user-entity/role.entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { UserCreditsDTO } from 'src/quest/AI-quest/AI-quest-dto/UserCredits.dto';
import { USER_DISABILTY_TYPES_OPTIONS } from 'src/user/user-profile/user-profile-dto/get-profile-response.dto';

@Injectable()
export class HrUsersService {
  constructor(
    private readonly userEnterpriseService: UserEnterpriseService,
    private readonly emailService: EmailService,
    private readonly s3Service: S3Service,
    private readonly hrUtilsService: HrUtilsService,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(DepartmentEntity)
    private readonly departmentRepo: Repository<DepartmentEntity>,

    @InjectRepository(EnterpriseEntity)
    private readonly enterpriseRepo: Repository<EnterpriseEntity>,

    @InjectRepository(EnterpriseDomainsEntity)
    private readonly enterpriseDomainRepo: Repository<EnterpriseDomainsEntity>,

    @InjectRepository(AccessTokenEntity)
    private readonly accessTokenRepo: Repository<AccessTokenEntity>,

    @InjectRepository(UserCreditsEntity)
    private readonly userCreditsRepo: Repository<UserCreditsEntity>,
  ) {}

  async deleteUser(userId: string, HR: UserEntity): Promise<UpdateUserResDTO> {
    // Validate and fetch user ID
    const userID = this.hrUtilsService.validateAndGetId(userId);

    if (userID === HR.id) {
      throw new BadRequestException("You can't delete your own account.");
    }

    // Fetch user with necessary relations
    const user = await this.userRepo.findOne({
      where: {
        id: userID,
        isAccountVerified: true,
        enterprise: { id: HR.enterprise.id },
        isDeleted: false,
      },
      relations: ['roles'],
    });

    if (!user) {
      throw new BadRequestException('User not found.');
    }

    // Check if the user is an admin
    const isAdmin = user.roles.some((role) => role.value === ROLE_VALUES.ADMIN);
    if (isAdmin) {
      throw new BadRequestException("Can't delete account of an Admin.");
    }

    // Update user's active status
    user.isDeleted = true;

    let tokensToDelete = await this.accessTokenRepo.find({
      where: { user: { id: user.id }, isDeleted: false },
    });

    tokensToDelete.forEach((token) => {
      token.isDeleted = true;
    });

    // Save all changes in parallel
    await Promise.all([
      this.accessTokenRepo.save(tokensToDelete),
      this.userRepo.save(user),
    ]);

    const enterprise = await this.enterpriseRepo.findOneBy({
      id: HR.enterprise.id,
    });
    enterprise.numOfUsers--;
    await this.enterpriseRepo.save(enterprise);

    return {
      error: false,
      msg: "User's Account has been deleted successfully.",
      user: UserProfileDto.transform(user),
    };
  }

  async updateUser(
    updateData: UpdateUserReqDTO,
    HR: UserEntity,
    userId: string,
  ): Promise<UpdateUserResDTO> {
    const { isActive, assignHrRole } = updateData;

    // Validate and fetch user ID
    const userID = this.hrUtilsService.validateAndGetId(userId);
    if (userID === HR.id) {
      throw new BadRequestException("You can't update your own status.");
    }

    // Fetch user with necessary relations
    const user = await this.userRepo.findOne({
      where: {
        id: userID,
      },
      relations: ['roles'],
    });
    if (!user) {
      throw new BadRequestException('User not found.');
    }

    // Check if the user is an admin
    const isAdmin = user.roles.some((role) => role.value === ROLE_VALUES.ADMIN);
    if (isAdmin) {
      throw new BadRequestException("Can't update status of an Admin.");
    }

    // Fetch HR Role
    const hrRole = await this.roleRepo.findOne({
      where: { value: ROLE_VALUES.HR },
    });
    if (!hrRole) {
      throw new BadRequestException('HR role not found.');
    }

    // Check or assign HR role
    if (assignHrRole) {
      const isHR = user.roles.some((role) => role.value === ROLE_VALUES.HR);
      if (!isHR) {
        user.roles.push(hrRole);
      }
    } else {
      const HrIndex = user.roles.findIndex(
        (role) => role.value === ROLE_VALUES.HR,
      );

      if (HrIndex !== -1) {
        user.roles.splice(HrIndex, 1);
      }
    }
    if (updateData.tags !== undefined) {
      user.tags = updateData.tags.length > 0 ? updateData.tags : ['Employee'];
    }
    // Update user's active status
    user.isActive = updateData.isActive;
    await this.userRepo.save(user);

    // Transform and return updated user response
    return {
      error: false,
      msg: 'User status updated.',
      user: UserProfileDto.transform(user),
    };
  }

  async addSingleUser(
    body: AddSingleUserReqDTO,
    HR: UserEntity,
  ): Promise<AddSingleUserResDTO> {
    try {
      const { email, firstName, lastName, departmentId, designation } = body;
      let newUser = new UserEntity();

      // add enterprise check for email
      const { mailDomain, mailEnterprise } =
        this.userEnterpriseService.extractDomainInfo(email);

      await this.userEnterpriseService.checkForConsumerEmail(email);

      const existingEnterprise = await this.enterpriseRepo.findOne({
        where: { name: mailEnterprise },
      });

      if (!existingEnterprise || existingEnterprise.id !== HR.enterprise.id) {
        throw new BadRequestException(
          'The email is not linked to your enterprise. Please use a valid company-issued email.',
        );
      }

      // check enterprise domain status

      const existingEnterpriseDomain = await this.enterpriseDomainRepo.findOne({
        where: {
          enterprise: { id: existingEnterprise.id },
          domain: mailDomain,
        },
      });

      if (!existingEnterpriseDomain) {
        // create new domain
        const newEnterpriseDomain = new EnterpriseDomainsEntity();
        newEnterpriseDomain.enterprise = existingEnterprise;
        newEnterpriseDomain.domain = mailDomain;
        await this.enterpriseDomainRepo.save(newEnterpriseDomain);
      }

      if (
        existingEnterpriseDomain &&
        existingEnterpriseDomain.status !== ENTERPRISE_DOMAIN_STATUS.ACTIVE
      ) {
        throw new BadRequestException(
          'The email domain for this enterprise is inactive. Please contact your administrator.',
        );
      }

      // check user with email already exists in DB
      const existingUser = await this.userRepo.findOne({
        where: { email, isDeleted: false },
      });

      if (existingUser) {
        throw new BadRequestException('User with email already exists.');
      }

      newUser.email = email;
      newUser.firstName = firstName;
      newUser.lastName = lastName;
      newUser.designation = designation;
      newUser.enterprise = existingEnterprise;
      newUser.tags = body.tags || ['Employee'];

      const department = await this.departmentRepo.findOne({
        where: { id: departmentId },
      });

      if (!department) {
        throw new BadRequestException(
          'Department not found, Please check the ID.',
        );
      }

      newUser.department = department;
      newUser.isAccountVerified = true;

      const role = await this.roleRepo.findOne({
        where: { value: ROLE_VALUES.USER },
      });

      if (!role) {
        throw new BadRequestException('Role not found !!');
      }

      newUser.roles = [role];

      // generate random password for user and save user
      const password = this.generatePassword();
      const passwordHash = await argon2.hash(password);

      newUser.password = passwordHash;
      newUser = await this.userRepo.save(newUser);

      const enterprise = await this.enterpriseRepo.findOneBy({
        id: existingEnterprise.id,
      });
      enterprise.numOfUsers++;
      await this.enterpriseRepo.save(enterprise);

      // send password via mail to new user email
      const { subject, emailBody, fromEmail, HRAddUserTemplate } =
        ConfigService.PROPERTIES().hrAddUserEmail;

      const capitalizedEnterpriseName =
        existingEnterprise.name.charAt(0).toUpperCase() +
        existingEnterprise.name.slice(1);

      const textBody = emailBody.replace(
        '$$Company',
        capitalizedEnterpriseName,
      );

      const html = HRAddUserTemplate.replace('$$Email', email)
        .replace('$$Password', password)
        .replace('$$name', `${firstName} ${lastName}`);

      const newSubject = subject.replace(
        '$$Company',
        capitalizedEnterpriseName,
      );

      await this.emailService.sendTextMail({
        toEmail: email,
        fromEmail,
        subject: newSubject,
        textBody,
        html,
      });

      const newuserProfile = UserProfileDto.transform(newUser);

      return {
        error: false,
        msg: 'User Added Successfully!',
        user: newuserProfile,
      };
    } catch (error) {
      throw error;
    }
  }

  private generatePassword(): string {
    const lowercaseChars = 'abcdefghijklmnopqrstuvwxyz';
    const uppercaseChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '0123456789';
    const specialChars = '!@#$%^&*';

    // Ensure each required character type is present
    const getRandomChar = (chars: string) =>
      chars[Math.floor(Math.random() * chars.length)];

    const passwordArray = [
      getRandomChar(lowercaseChars), // 1 lowercase
      getRandomChar(uppercaseChars), // 1 uppercase
      getRandomChar(numbers), // 1 number
      getRandomChar(specialChars), // 1 special character
    ];

    // Fill the remaining length with random characters from all sets
    const allChars = lowercaseChars + uppercaseChars + numbers + specialChars;

    for (let i = 4; i < 10; i++) {
      passwordArray.push(getRandomChar(allChars));
    }

    // Shuffle the password to avoid predictable character positions
    for (let i = passwordArray.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [passwordArray[i], passwordArray[j]] = [
        passwordArray[j],
        passwordArray[i],
      ];
    }

    return passwordArray.join('');
  }

  //----------------------------------------------------------------------------------
  async addBulkUsers(
    csvFile: Express.Multer.File,
    HR: UserEntity,
  ): Promise<AddBulkUsersResDTO> {
    if (!csvFile) {
      throw new BadRequestException('Please provide the csv file to upload.');
    }

    const uncompiledUsers: { row: any; reason: string }[] = [];
    const compiledUsers: { user: UserEntity; password: string }[] = [];

    const rows = await this.parseCSV(csvFile);

    for (const row of rows) {
      try {
        const userDetails = this.extractUserDetails(row);

        if (this.isValidUser(userDetails)) {
          const result = await this.createUser(userDetails, HR);
          if (result.isError) {
            uncompiledUsers.push({ row, reason: result.error });
          } else {
            compiledUsers.push({
              user: result.user,
              password: result.password,
            });
          }
        } else {
          uncompiledUsers.push({
            row,
            reason:
              "Missing required fields: 'Email', 'First Name', 'Last Name', 'Department ID', 'Designation'.",
          });
        }
      } catch (error) {
        uncompiledUsers.push({
          row,
          reason: error.message || 'Unknown error occurred.',
        });
      }
    }

    await this.sendEmails(compiledUsers, uncompiledUsers, HR);

    let updatedUsersCount = 0;
    compiledUsers.map((item) => {
      if (item.password === 'SAME') {
        updatedUsersCount++;
      }
    });

    const addedUsersCount = compiledUsers.length - updatedUsersCount;

    return {
      error: false,
      msg: `${addedUsersCount > 0 ? addedUsersCount + ' users successfully added.' : ''}   
      ${updatedUsersCount > 0 ? updatedUsersCount + ' users has been updated.' : ''}
      ${uncompiledUsers.length > 0 ? `Unable to add ${uncompiledUsers.length} users.` : ''}  
      ${uncompiledUsers.length > 0 ? 'Report has been sent to you via email.' : ''}`,
    };
  }

  private async parseCSV(csvFile: Express.Multer.File): Promise<any[]> {
    const bufferStream = new stream.PassThrough();
    bufferStream.end(csvFile.buffer);

    return new Promise((resolve, reject) => {
      const rows: any[] = [];
      bufferStream
        .pipe(csvParser())
        .on('data', (row) => rows.push(row))
        .on('end', () => resolve(rows))
        .on('error', reject);
    });
  }

  private extractUserDetails(row: any) {
    return {
      email: row.Email,
      firstName: row['First Name'],
      lastName: row['Last Name'],
      departmentId: row['Department ID'],
      designation: row.Designation,
      tags: row.Tag ? row.Tag.split(',').map((t) => t.trim()) : undefined,
    };
  }

  private isValidUser(userDetails: any): boolean {
    return (
      userDetails.email &&
      userDetails.firstName &&
      userDetails.lastName &&
      userDetails.departmentId &&
      userDetails.designation
    );
  }

  private async sendEmails(
    compiledUsers: { user: UserEntity; password: string }[],
    uncompiledUsers: { row: any; reason: string }[],
    HR: UserEntity,
  ) {
    try {
      const {
        subject,
        emailBody,
        fromEmail,
        HRAddBulkUserTemplate,
        HRAddUserTemplate,
        HrUpdateUserTemplate,
      } = ConfigService.PROPERTIES().hrAddUserEmail;

      if (compiledUsers.length > 0) {
        const emailsToSend = compiledUsers.map(({ user, password }) => {
          if (password === 'SAME') {
            // send user update email
            const emailHTML = HrUpdateUserTemplate.replace(
              '$$Firstname',
              user.firstName,
            )
              .replace('$$Lastname', user.lastName)
              .replace('$$Designation', user.designation)
              .replace('$$Department', user.department.name)
              .replace('$$Email', user.email);

            return {
              fromEmail,
              toEmail: user.email,
              subject: 'Your Profile Has Been Updated by HR',
              textBody: 'Your Profile Has Been Updated by HR',
              html: emailHTML,
            };
          } else {
            // send user add email
            const capitalizedEnterpriseName = `${HR.enterprise.name.charAt(0).toUpperCase()}${HR.enterprise.name.slice(1)}`;
            const emailHTML = HRAddUserTemplate.replace('$$Email', user.email)
              .replace('$$Password', password)
              .replace('$$name', `${user.firstName} ${user.lastName}`);

            return {
              fromEmail,
              toEmail: user.email,
              subject: subject.replace('$$Company', capitalizedEnterpriseName),
              textBody: emailBody.replace(
                '$$Company',
                capitalizedEnterpriseName,
              ),
              html: emailHTML,
            };
          }
        });

        await this.emailService.sendBatchEmailsWithDelay(emailsToSend);
      }

      if (uncompiledUsers.length > 0) {
        // Convert uncompiled users to CSV
        const csvFields = ['row', 'reason'];
        const csvParser = new Parser({ fields: csvFields });
        const csvData = csvParser.parse(uncompiledUsers);

        // Convert CSV to Buffer
        const csvBuffer = Buffer.from(csvData);

        // Create a mock Multer file object
        const csvFile: Express.Multer.File = {
          fieldname: 'file',
          originalname: `uncompiled_users_${uuid()}.csv`,
          encoding: '7bit',
          mimetype: 'text/csv',
          buffer: csvBuffer,
          size: csvBuffer.length,
          stream: null, // Stream not used in this context
          destination: '',
          filename: '',
          path: '',
        };

        // Upload to S3
        const uploadedFile = await this.s3Service.uploadFile(csvFile);
        const url = uploadedFile.Location;

        const html = HRAddBulkUserTemplate.replace(
          '$$DATA',
          `Download CSV Report: <a href="${url}">${url}</a>`,
        ).replace('$$name', `${HR.firstName} ${HR.lastName}`);

        const textBody = `The following users could not be compiled. Download the report here: ${url}`;

        await this.emailService.sendTextMail({
          fromEmail,
          toEmail: HR.email,
          subject: 'Uncompiled Users Report',
          textBody,
          html,
        });
      }
    } catch (error) {
      throw error;
    }
  }

  private async createUser(
    body: AddSingleUserReqDTO,
    HR: UserEntity,
  ): Promise<CreateUserResInterface> {
    try {
      const { email, firstName, lastName, departmentId, designation } = body;

      // add enterprise check for email
      const { mailDomain, mailEnterprise } =
        this.userEnterpriseService.extractDomainInfo(email);

      await this.userEnterpriseService.checkForConsumerEmail(email);

      const existingEnterprise = await this.enterpriseRepo.findOne({
        where: { name: mailEnterprise },
      });

      if (!existingEnterprise || existingEnterprise.id !== HR.enterprise.id) {
        throw new BadRequestException(
          'The email is not linked to your enterprise. Please use a valid company-issued email.',
        );
      }

      // check enterprise domain status
      const existingEnterpriseDomain = await this.enterpriseDomainRepo.findOne({
        where: { enterprise: { id: existingEnterprise.id } },
      });

      if (existingEnterpriseDomain.status !== ENTERPRISE_DOMAIN_STATUS.ACTIVE) {
        throw new BadRequestException(
          'The email domain for this enterprise is inactive. Please contact your administrator.',
        );
      }

      // check user with email already exists in DB
      const existingUser = await this.userRepo.findOne({
        where: { email, isDeleted: false },
        relations: ['department', 'roles'],
      });

      if (existingUser) {
        // update user data

        if (
          existingUser.roles.some(
            (item) =>
              item.value === ROLE_VALUES.HR || item.value === ROLE_VALUES.ADMIN,
          )
        ) {
          throw new BadRequestException(
            `Update Unavailable: This User Has Privileged Access (HR/Admin)`,
          );
        }

        if (existingUser.roles) existingUser.firstName = firstName;
        existingUser.lastName = lastName;
        existingUser.designation = designation;
        existingUser.tags = body.tags ?? existingUser.tags;

        const department = await this.departmentRepo.findOne({
          where: { id: departmentId },
        });

        if (!department) {
          throw new BadRequestException(
            'Department not found, Please check the ID.',
          );
        }

        existingUser.department = department;
        await this.userRepo.save(existingUser);

        return {
          isError: false,
          error: '',
          user: existingUser,
          password: 'SAME',
        };
      } else {
        let newUser = new UserEntity();

        newUser.email = email;
        newUser.firstName = firstName;
        newUser.lastName = lastName;
        newUser.designation = designation;
        newUser.enterprise = existingEnterprise;
        newUser.tags = body.tags || ['Employee'];

        const department = await this.departmentRepo.findOne({
          where: { id: departmentId },
        });

        if (!department) {
          throw new BadRequestException(
            'Department not found, Please check the ID.',
          );
        }

        newUser.department = department;
        newUser.isAccountVerified = true;

        const role = await this.roleRepo.findOne({
          where: { value: ROLE_VALUES.USER },
        });
        if (!role) {
          throw new BadRequestException('Role not found !!');
        }

        newUser.roles = [role];

        // generate random password for user and save user
        const password = this.generatePassword();
        const passwordHash = await argon2.hash(password);

        newUser.password = passwordHash;
        newUser = await this.userRepo.save(newUser);

        const enterprise = await this.enterpriseRepo.findOneBy({
          id: existingEnterprise.id,
        });
        enterprise.numOfUsers++;
        await this.enterpriseRepo.save(enterprise);

        return {
          isError: false,
          error: '',
          user: newUser,
          password,
        };
      }
    } catch (error) {
      return {
        isError: true,
        error: error.message || 'Some Errreason',
        user: null,
        password: null,
      };
    }
  }

  async getAllUsers(
    HR: UserEntity,
    query: GetAllUsersFilterQueryInterface,
  ): Promise<GetAllUsersResDTO> {
    const { page = '1', limit = '10', name } = query;

    const { limit: take, offset } =
      this.hrUtilsService.parsePageNumberAndGetlimitAndOffset(
        page,
        parseInt(limit, 10),
      );

    const admin = await this.userRepo.findOne({
      where: { isDeleted: false, roles: { value: ROLE_VALUES.ADMIN } },
    });

    let whereCondition: any = {
      id: Not(admin.id),
      isDeleted: false,
      enterprise: { id: HR.enterprise.id },
    };

    if (name) {
      whereCondition = [
        { ...whereCondition, firstName: Like(`%${name}%`) },
        { ...whereCondition, lastName: Like(`%${name}%`) },
      ];
    }

    const [users, total] = await this.userRepo.findAndCount({
      where: whereCondition,
      relations: ['enterprise', 'department'],
      take,
      skip: offset,
    });

    const usersResp = users.map((item) => HRUserListDetailsDTO.transform(item));

    return {
      error: false,
      total,
      nbHits: users.length,
      users: usersResp,
    };
  }

  async getSingleUserProfile(
    HR: UserEntity,
    userId: string,
  ): Promise<GetSingleUserProfileResDTO> {
    const id = this.hrUtilsService.validateAndGetId(userId);

    const user = await this.userRepo.findOne({
      where: { id },
      relations: ['enterprise', 'roles', 'department'],
    });

    if (!user) {
      throw new BadRequestException('User not found !!');
    }

    const userResp = UserProfileDto.transform(user);

    const disabilityLabel =
      user.disability === USER_DISABILTY_TYPES.YES
        ? USER_DISABILTY_TYPES_OPTIONS.LIMITATION
        : user.disability === USER_DISABILTY_TYPES.NO
          ? USER_DISABILTY_TYPES_OPTIONS.NO_LIMITATION
          : USER_DISABILTY_TYPES_OPTIONS.PREFER_NOT_TO_SAY;

    return {
      error: false,
      user: { ...userResp, disability: disabilityLabel },
    };
  }

  async getSingleUserQuestMetricsHistory(
    HR: UserEntity,
    userId: string,
    query: GetSingleUserQuestMetricsHistoryFilterQueryInterface,
  ): Promise<GetSingleUserQuestMetricsHistoryResDTO> {
    const { page } = query;

    const userID = this.hrUtilsService.validateAndGetId(userId);

    // Fetch user with necessary relations
    const user = await this.userRepo.findOne({
      where: {
        id: userID,
        isAccountVerified: true,
        enterprise: { id: HR.enterprise.id },
        isDeleted: false,
      },
      relations: ['roles'],
    });

    if (!user) {
      throw new BadRequestException('User not found.');
    }

    const { limit, offset } =
      this.hrUtilsService.parsePageNumberAndGetlimitAndOffset(page, 10);

    const [UserQuestMetricsHistory, total] =
      await this.userCreditsRepo.findAndCount({
        where: { user: { id: user.id } },
        relations: ['user', 'quest', 'enterprise'],
        take: limit,
        skip: offset,
      });

    const resp = UserQuestMetricsHistory.map((item) =>
      UserCreditsDTO.transform(item),
    );

    return {
      error: false,
      total,
      nbHits: resp.length,
      metricsHistory: resp,
    };
  }
}
