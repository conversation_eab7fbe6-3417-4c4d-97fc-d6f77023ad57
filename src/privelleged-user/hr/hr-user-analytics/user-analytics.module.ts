import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserAggregateController } from './user-analytics.controller';
import { UserAggregateService } from './user-analytics.service';
import { UserEntity, AccessTokenEntity } from 'src/models/user-entity';
import {
  QuestEntity,
  QuestParticipantEntity,
  QuestTypesEntity,
} from 'src/models/quest-entity';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { QuestCompletionProofMediaEntity } from 'src/models/quest-entity/quest-completion-proof-media.entity';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { CustomLogger } from 'src/common/logger/custom-logger.service';
import { CommonModule } from 'src/common/common.module';
import { ThirdPartyModule } from 'src/third-party/third-party.module';
import { SecurityModule } from 'src/security/security.module';
import { UserQuestMetricsEntity } from 'src/models/metrics-entity/user-quest-metrics.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      UserEntity,
      QuestEntity,
      QuestParticipantEntity,
      QuestTypesEntity,
      UserCreditsEntity,
      MCQQuestionEntity,
      QuestCompletionProofMediaEntity,
      DepartmentEntity,
      AccessTokenEntity,
      UserQuestMetricsEntity,
    ]),
    CommonModule,
    ThirdPartyModule,
    SecurityModule,
  ],
  controllers: [UserAggregateController],
  providers: [UserAggregateService, UserProfileService, CustomLogger],
  exports: [UserAggregateService],
})
export class UserAggregateModule {}
