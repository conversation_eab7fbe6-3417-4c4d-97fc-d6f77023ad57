import { <PERSON>, Get, Param, UseGuards, <PERSON>q, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { Request } from 'express';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserAggregateService } from './user-analytics.service';
import { UserAggregateResponseDto } from './dto/user-analytics-response.dto';
import { ErrorResponse } from 'src/common/responses/errorResponse';

@ApiTags('User Analytics')
@ApiBearerAuth()
@Controller('product-knowledge')
export class UserAggregateController {
  constructor(
    private readonly userAggregateService: UserAggregateService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Get aggregated user data including quest participation and scores',
    type: UserAggregateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'User not found',
    type: ErrorResponse,
  })
  @ApiParam({
    name: 'userId',
    description: 'ID of the user to get aggregated data for',
    required: true,
    type: Number,
  })
  @Get(':userId')
  @UseGuards(AuthGuard)
  @Authority('VIEW_USER_DATA')
  async getUserAggregateData(
    @Param('userId', ParseIntPipe) userId: number,
    @Req() req: Request,
  ): Promise<UserAggregateResponseDto> {
    await this.userProfileService.getUserFromToken(req);
    return this.userAggregateService.getUserAggregateData(userId);
  }
}
