import { ApiProperty } from '@nestjs/swagger';
import { BaseResponse } from 'src/common/responses/baseResponse.dto';
import { QUEST_SCOPE } from 'src/models/quest-entity'; // Use the quest entity enum

export class QuestEvaluationDto {
  @ApiProperty({ description: 'Strength areas identified in the quest response', required: false })
  strength?: string;

  @ApiProperty({ description: 'Weakness areas identified in the quest response', required: false })
  weakness?: string;

  @ApiProperty({ description: 'Recommendation for improvement', required: false })
  recommendation?: string;
}

export class QuestResponseDto {
  @ApiProperty({ description: 'Quest ID' })
  id: number;

  @ApiProperty({ description: 'Quest title' })
  title: string;

  @ApiProperty({ description: 'Quest description' })
  description: string;

  @ApiProperty({
    description: 'Quest type information',
    type: String,
    required: false,
  })
  questType?: string;

  @ApiProperty({
    enum: QUEST_SCOPE,
    description: 'Scope of the quest (e.g., ENTERPRISE, PERSONAL)',
  })
  scope: QUEST_SCOPE;

  @ApiProperty({ description: 'Quest type (MCQ, TEXT, etc)' })
  type: string;

  @ApiProperty({ description: 'Date when quest was completed' })
  completionDate: string;

  @ApiProperty({ description: 'Credits earned for this quest' })
  creditsEarned: number;

  @ApiProperty({ description: 'Total possible credits for this quest', required: false })
  completionCredits?: number;

  @ApiProperty({ description: 'User response/submission' })
  userResponse: string | any;

  @ApiProperty({ description: 'Score achieved (for MCQ quests)' })
  score?: number;

  @ApiProperty({ description: 'Total possible score' })
  totalPossible?: number;

  @ApiProperty({
    description: 'AI-generated analysis score for text answers (100-1000)',
    required: false,
  })
  userQuestScore?: number;

  @ApiProperty({
    description: 'AI-generated evaluation of quest performance',
    required: false,
    type: QuestEvaluationDto,
  })
  evaluation?: any;
}

export class UserStrengthsWeaknessesDto {
  @ApiProperty({ description: 'Areas of strength' })
  strengths: string[];

  @ApiProperty({ description: 'Areas that need improvement' })
  weaknesses: string[];

  @ApiProperty({ description: 'AI-generated evaluation' })
  evaluation: string;
}

export class UserAggregateResponseDto extends BaseResponse {
  @ApiProperty({ description: 'User ID' })
  userId: number;

  @ApiProperty({ description: 'First name of the user' })
  firstName: string;

  @ApiProperty({ description: 'Last name of the user' })
  lastName: string;

  @ApiProperty({ description: 'User email' })
  email: string;

  @ApiProperty({ description: 'Total credit score (100-1000)' })
  creditScore: number;

  @ApiProperty({ description: 'Knowledge score' })
  knowledgeScore: number;

  @ApiProperty({ description: 'Total quests available' })
  totalQuests: number;

  @ApiProperty({ description: 'Number of quests completed' })
  completedQuests: number;

  @ApiProperty({ description: 'Average score across all quests' })
  averageScore: number;

  @ApiProperty({ type: [QuestResponseDto], description: 'Details of each quest' })
  quests: QuestResponseDto[];

  @ApiProperty({ type: UserStrengthsWeaknessesDto, description: 'AI evaluation of user strengths and weaknesses' })
  evaluation: UserStrengthsWeaknessesDto;
}
