import { Injectable, Logger, OnModuleInit, OnM<PERSON>ule<PERSON><PERSON>roy, Scope } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { MonthlyRewardsHistory } from 'src/models/rewards-entity/MonthlyRewardsHistory.entity';
import { EnterpriseEntity } from 'src/models/user-entity/enterprise.entity';
import { GetSelectedProductsPublicResponseDto, GetTopThreeUsersResponseDto } from 'src/products/dtos';
import { ProductsService } from 'src/products/products.service';

@Injectable({ scope: Scope.DEFAULT })
export class RewardsHistoryService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RewardsHistoryService.name);
  
  private checkInterval: NodeJS.Timeout | null = null;
  private nextScheduledUpdate: NodeJS.Timeout | null = null;
  private pendingRetry: { year: number; month: number; timeout: NodeJS.Timeout | null } | null = null;
  private recoveryTimeouts: Set<NodeJS.Timeout> = new Set();
  
  private readonly CHECK_INTERVAL_MS = 3600000;
  private readonly END_OF_DAY_HOUR = 23;
  private readonly END_OF_DAY_MINUTE = 56;
  private readonly MS_PER_DAY = 86400000;
  private readonly MAX_RETRY_ATTEMPTS = 3;
  private readonly API_TIMEOUT_MS = 10000;
  private readonly MAX_CONCURRENT_RECOVERIES = 3;
  private readonly MAX_SAFE_TIMEOUT = 2147483647;
  
  private recoveryQueue: Array<{year: number, month: number}> = [];
  private activeRecoveries = 0;
  
  constructor(
    @InjectRepository(MonthlyRewardsHistory)
    private rewardsHistoryRepo: Repository<MonthlyRewardsHistory>,
    private productsService: ProductsService,
    @InjectRepository(EnterpriseEntity)
    private enterpriseRepo: Repository<EnterpriseEntity>,
  ) {}
  
  onModuleInit() {
    this.startBackupChecker();
    this.scheduleNextMonthUpdate();
    setTimeout(() => this.checkForMissingMonths(), 120000);
  }

  onModuleDestroy() {
    this.stopBackupChecker();
    
    if (this.nextScheduledUpdate) {
      clearTimeout(this.nextScheduledUpdate);
      this.nextScheduledUpdate = null;
    }
    
    if (this.pendingRetry?.timeout) {
      clearTimeout(this.pendingRetry.timeout);
      this.pendingRetry = null;
    }
    
    for (const timeout of this.recoveryTimeouts) {
      clearTimeout(timeout);
    }
    this.recoveryTimeouts.clear();
  }

  private async hasRunForMonth(year: number, month: number): Promise<boolean> {
    try {
      const firstDay = new Date(year, month, 1, 0, 0, 0);
      const lastDay = new Date(year, month + 1, 0, 23, 59, 59);
      
      const query = this.rewardsHistoryRepo.createQueryBuilder('history')
        .where('history.createdAt BETWEEN :firstDay AND :lastDay', { 
          firstDay: firstDay.toISOString(),
          lastDay: lastDay.toISOString() 
        });
      
      const count = await query.getCount();
      
      if (count > 0) {
        this.logger.debug(`Found ${count} existing records for ${this.formatYearMonthReadable(year, month)}`);
      }
      
      return count > 0;
    } catch (error) {
      this.logger.error(`Error checking if rewards history exists: ${error.message}`, error.stack);
      return false;
    }
  }

  private startBackupChecker(): void {
    this.stopBackupChecker();
    
    this.logger.log('Starting rewards history backup checker');
    
    this.checkInterval = setInterval(() => {
      this.checkAndRunUpdate();
    }, this.CHECK_INTERVAL_MS);
    
    this.logger.log(`Rewards history backup checker started - checking every ${this.CHECK_INTERVAL_MS / 60000} minutes`);
  }

  private stopBackupChecker(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
      this.logger.log('Rewards history backup checker stopped');
    }
  }

  private getEndOfMonthDate(year: number, month: number): Date {
    const lastDay = new Date(year, month + 1, 0);
    lastDay.setHours(this.END_OF_DAY_HOUR, this.END_OF_DAY_MINUTE, 0, 0);
    return lastDay;
  }

  private isUpdateScheduledFor(year: number, month: number): boolean {
    if (!this.nextScheduledUpdate) {
      return false;
    }
    
    const targetDate = this.getEndOfMonthDate(year, month);
    const now = new Date();
    const expectedDelay = targetDate.getTime() - now.getTime();
    
    if (expectedDelay > 0) {
      return true;
    }
    
    return false;
  }

  private scheduleNextMonthUpdate(): void {
    if (this.nextScheduledUpdate) {
      clearTimeout(this.nextScheduledUpdate);
      this.nextScheduledUpdate = null;
    }
    
    const now = new Date();
    
    let targetMonth = now.getMonth();
    let targetYear = now.getFullYear();
    
    const endOfCurrentMonth = this.getEndOfMonthDate(targetYear, targetMonth);
    
    if (now > endOfCurrentMonth) {
      if (targetMonth === 11) {
        targetMonth = 0;
        targetYear++;
      } else {
        targetMonth++;
      }
    }
    
    const targetDate = this.getEndOfMonthDate(targetYear, targetMonth);
    const delay = targetDate.getTime() - now.getTime();
    
    this.logger.log(`Scheduling update for ${this.formatYearMonthReadable(targetYear, targetMonth)} on ${targetDate.toLocaleString()}`);
    
    this.safeSetTimeout(async () => {
      this.logger.log(`Executing scheduled monthly rewards update for ${this.formatYearMonthReadable(targetYear, targetMonth)}`);
      
      try {
        const alreadyRun = await this.hasRunForMonth(targetYear, targetMonth);
        if (!alreadyRun) {
          await this.updateHistory(targetYear, targetMonth, true);
          this.logger.log(`Scheduled update completed for ${this.formatYearMonthReadable(targetYear, targetMonth)}`);
        } else {
          this.logger.log(`Scheduled update skipped - data exists for ${this.formatYearMonthReadable(targetYear, targetMonth)}`);
        }
      } catch (error) {
        this.logger.error(`Scheduled update failed for ${this.formatYearMonthReadable(targetYear, targetMonth)}: ${error.message}`, error.stack);
        this.scheduleRetry(targetYear, targetMonth, 1);
      } finally {
        this.scheduleNextMonthUpdate();
      }
    }, delay);
  }

  private safeSetTimeout(callback: () => void, delay: number): NodeJS.Timeout {
    if (delay <= this.MAX_SAFE_TIMEOUT) {
      return setTimeout(callback, delay);
    }
    
    const intermediateCallback = () => {
      const remainingDelay = delay - this.MAX_SAFE_TIMEOUT;
      if (remainingDelay <= 0) {
        callback();
      } else {
        this.safeSetTimeout(callback, remainingDelay);
      }
    };
    
    return setTimeout(intermediateCallback, this.MAX_SAFE_TIMEOUT);
  }
  
  private async checkAndRunUpdate(): Promise<void> {
    try {
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth();
      
      const isLastDayOfMonth = this.isLastDayOfMonth(now);
      
      if (isLastDayOfMonth && 
          now.getHours() >= this.END_OF_DAY_HOUR && 
          now.getMinutes() >= this.END_OF_DAY_MINUTE) {
            
        const alreadyRun = await this.hasRunForMonth(currentYear, currentMonth);
        if (alreadyRun) {
          this.logger.debug(`Backup check: Rewards history already exists for ${this.formatYearMonthReadable(currentYear, currentMonth)}`);
          return;
        }
        
        this.logger.log(`Backup check running update for ${this.formatYearMonthReadable(currentYear, currentMonth)}`);
        
        await this.updateHistory(currentYear, currentMonth, true)
          .then(() => {
            this.logger.log(`Backup-triggered update successful for ${this.formatYearMonthReadable(currentYear, currentMonth)}`);
          })
          .catch(error => {
            this.logger.error(`Backup update failed: ${error.message}`, error.stack);
            this.scheduleRetry(currentYear, currentMonth, 1);
          });
      }
    } catch (error) {
      this.logger.error(`Error in backup checker: ${error.message}`, error.stack);
    }
  }
  
  private scheduleRetry(year: number, month: number, attempt: number): void {
    if (attempt > this.MAX_RETRY_ATTEMPTS) {
      this.logger.error(`Maximum retry attempts (${this.MAX_RETRY_ATTEMPTS}) reached for ${this.formatYearMonthReadable(year, month)}`);
      return;
    }
    
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const currentTotalMonths = currentYear * 12 + currentMonth;
    const targetTotalMonths = year * 12 + month;
    
    if (targetTotalMonths < currentTotalMonths) {
      this.logger.log(`Skipping retry for historical month ${this.formatYearMonthReadable(year, month)}`);
      return;
    }
    
    const delayMinutes = 1 * Math.pow(2, attempt - 1);
    const RETRY_DELAY_MS = delayMinutes * 60 * 1000;
    
    this.logger.log(`Scheduling retry #${attempt} in ${delayMinutes} minutes for ${this.formatYearMonthReadable(year, month)}`);
    
    if (this.pendingRetry?.timeout) {
      clearTimeout(this.pendingRetry.timeout);
    }
    
    const timeout = this.safeSetTimeout(async () => {
      this.logger.log(`Executing retry #${attempt} for ${this.formatYearMonthReadable(year, month)}`);
      
      const alreadyRun = await this.hasRunForMonth(year, month);
      if (!alreadyRun) {
        try {
          const now = new Date();
          const currentYear = now.getFullYear();
          const currentMonth = now.getMonth();
          
          const currentTotalMonths = currentYear * 12 + currentMonth;
          const targetTotalMonths = year * 12 + month;
          
          if (targetTotalMonths < currentTotalMonths) {
            this.logger.log(`Skipping historical month ${this.formatYearMonthReadable(year, month)}`);
          } else {
            const forceProcessing = (year === currentYear && month === currentMonth && 
                now.getDate() >= new Date(year, month + 1, 0).getDate() - 2);
                
            await this.updateHistory(year, month, forceProcessing);
          }
          
          this.logger.log(`Retry #${attempt} successful for ${this.formatYearMonthReadable(year, month)}`);
        } catch (error) {
          this.logger.error(`Retry #${attempt} failed: ${error.message}`, error.stack);
          this.scheduleRetry(year, month, attempt + 1);
        }
      } else {
        this.logger.log(`Retry skipped: data exists for ${this.formatYearMonthReadable(year, month)}`);
      }
      
      this.pendingRetry = null;
    }, RETRY_DELAY_MS);
    
    this.pendingRetry = { year, month, timeout };
  }

  private isLastDayOfMonth(date: Date): boolean {
    const nextMonth = new Date(date.getFullYear(), date.getMonth() + 1);
    const lastDay = new Date(nextMonth.getTime() - this.MS_PER_DAY);
    
    return date.getDate() === lastDay.getDate();
  }

  public async runManualUpdate(): Promise<void> {
    this.logger.log('Manual update triggered by admin');
    await this.updateHistory(undefined, undefined, true);
    this.scheduleNextMonthUpdate();
    this.logger.log('Manual update completed');
  }

  async updateHistory(year?: number, month?: number, forceCurrentMonth: boolean = false): Promise<void> {
    const targetYear = year ?? new Date().getFullYear();
    const targetMonth = month ?? new Date().getMonth();
    
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    if (targetYear === currentYear && targetMonth === currentMonth && !forceCurrentMonth) {
      const lastDayOfMonth = new Date(currentYear, currentMonth + 1, 0).getDate();
      const currentDay = now.getDate();
      
      if (currentDay < lastDayOfMonth - 2) {
        this.logger.warn(`SAFETY CHECK: Prevented premature processing of current month ${this.formatYearMonthReadable(targetYear, targetMonth)}`);
        return;
      }
      
      this.logger.log(`Processing current month ${this.formatYearMonthReadable(targetYear, targetMonth)} (in last 3 days of month)`);
    }
    
    this.logger.log(`Starting update for ${this.formatYearMonthReadable(targetYear, targetMonth)}`);
    
    try {
      const enterprises = await this.enterpriseRepo.find();
      this.logger.log(`Found ${enterprises.length} enterprises to process`);
      
      let successCount = 0;
      let failureCount = 0;
      
      for (const enterprise of enterprises) {
        try {
          const enterpriseId = enterprise.id;          const topThreePromise = Promise.race<GetTopThreeUsersResponseDto>([
            this.productsService.getTopThreeUsersForSpecificMonth(enterpriseId, targetYear, targetMonth),
            new Promise<never>((_, reject) => 
              setTimeout(() => reject(new Error('API timeout for getTopThreeUsersResponse')), this.API_TIMEOUT_MS)
            )
          ]);
          
          const rewardsPromise = Promise.race<GetSelectedProductsPublicResponseDto>([
            this.productsService.getSelectedProductsPublic(enterpriseId),
            new Promise<never>((_, reject) => 
              setTimeout(() => reject(new Error('API timeout for getSelectedProductsPublic')), this.API_TIMEOUT_MS)
            )
          ]);
          
          const [topThreeResponse, rewardsResponse] = await Promise.all([
            topThreePromise,
            rewardsPromise
          ]);
          
          await this.rewardsHistoryRepo.manager.transaction(async transactionalEntityManager => {
            const history = this.rewardsHistoryRepo.create({
              enterpriseId,
              topPerformers: JSON.stringify(topThreeResponse.data),
              rewards: JSON.stringify(rewardsResponse.data),
            });
            await transactionalEntityManager.save(history);
          });
          
          this.logger.log(`Updated rewards for enterprise ${enterpriseId}`);
          successCount++;
        } catch (error) {
          failureCount++;
          this.logger.error(`Error updating rewards for enterprise ${enterprise.id}: ${error.message}`, error.stack);
        }
      }
      
      this.logger.log(`Completed update for ${this.formatYearMonthReadable(targetYear, targetMonth)}. Success: ${successCount}, Failures: ${failureCount}`);
    } catch (error) {
      this.logger.error(`Error in batch update: ${error.message}`, error.stack);
      throw error;
    }
  }

  private async checkForMissingMonths(): Promise<void> {
    try {
      this.logger.log('Checking for missing months in rewards history');
      
      const now = new Date();
      const currentYear = now.getFullYear();
      const currentMonth = now.getMonth();
      
      if (!this.isUpdateScheduledFor(currentYear, currentMonth)) {
        this.logger.log(`Current month ${this.formatYearMonthReadable(currentYear, currentMonth)} not scheduled yet`);
        this.scheduleNextMonthUpdate();
      }
      
      const nextMonth = currentMonth < 11 ? currentMonth + 1 : 0;
      const nextYear = currentMonth < 11 ? currentYear : currentYear + 1;
      
      if (!this.isUpdateScheduledFor(nextYear, nextMonth)) {
        this.logger.log(`Next month ${this.formatYearMonthReadable(nextYear, nextMonth)} should be scheduled soon`);
      }
      
      this.logger.log('Completed check for missing months');
    } catch (error) {
      this.logger.error(`Error checking for missing months: ${error.message}`, error.stack);
    }
  }
  
  private processRecoveryQueue(): void {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    const currentTotalMonths = currentYear * 12 + currentMonth;
    
    this.recoveryQueue = this.recoveryQueue.filter(item => {
      const itemTotalMonths = item.year * 12 + item.month;
      return itemTotalMonths >= currentTotalMonths;
    });
    
    while (this.recoveryQueue.length > 0 && this.activeRecoveries < this.MAX_CONCURRENT_RECOVERIES) {
      const item = this.recoveryQueue.shift();
      if (item) {
        this.scheduleRecovery(item.year, item.month);
        this.activeRecoveries++;
      }
    }
  }
  
  private scheduleRecovery(year: number, month: number): void {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    const currentTotalMonths = currentYear * 12 + currentMonth;
    const targetTotalMonths = year * 12 + month;
    
    if (targetTotalMonths < currentTotalMonths) {
      this.logger.log(`Skipping historical month ${this.formatYearMonthReadable(year, month)}`);
      this.activeRecoveries--;
      this.processRecoveryQueue();
      return;
    }
    
    if (year === currentYear && month === currentMonth && this.isUpdateScheduledFor(year, month)) {
      this.logger.log(`Month ${this.formatYearMonthReadable(year, month)} already scheduled`);
      this.activeRecoveries--;
      this.processRecoveryQueue();
      return;
    }
    
    const delayMinutes = 5;
    const delayMs = delayMinutes * 60 * 1000;
    
    this.logger.log(`Scheduling recovery for ${this.formatYearMonthReadable(year, month)} in ${delayMinutes} minutes`);
    
    const timeout = this.safeSetTimeout(async () => {
      try {
        this.recoveryTimeouts.delete(timeout);
        
        const currentNow = new Date();
        const currentNowYear = currentNow.getFullYear();
        const currentNowMonth = currentNow.getMonth();
        
        const currentNowTotalMonths = currentNowYear * 12 + currentNowMonth;
        const targetTotalMonths = year * 12 + month;
        
        if (targetTotalMonths < currentNowTotalMonths) {
          this.logger.log(`Skipping historical month ${this.formatYearMonthReadable(year, month)}`);
        } 
        else {
          const alreadyExists = await this.hasRunForMonth(year, month);
          if (!alreadyExists) {
            this.logger.log(`Starting recovery for month ${this.formatYearMonthReadable(year, month)}`);
            try {
              await this.updateHistory(year, month, true);
              this.logger.log(`Successfully recovered data for ${this.formatYearMonthReadable(year, month)}`);
            } catch (error) {
              this.logger.error(`Failed to recover data for ${this.formatYearMonthReadable(year, month)}: ${error.message}`, error.stack);
              this.scheduleRetry(year, month, 1);
            }
          } else {
            this.logger.log(`Skipping recovery - data exists for ${this.formatYearMonthReadable(year, month)}`);
          }
        }
      } finally {
        this.activeRecoveries--;
        this.processRecoveryQueue();
      }
    }, delayMs);
    
    this.recoveryTimeouts.add(timeout);
  }
  
  private formatYearMonthReadable(year: number, month: number): string {
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return `${monthNames[month]} ${year}`;
  }
}