import { Controller, Get, Put, Param, Body, ParseIntPipe, UseGuards, Query, Req, Post } from '@nestjs/common';
import { HrProductsService } from './hr-products.service';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { Authority } from 'src/security/middleware/authority.decorator';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';

import { RewardCatalogueDto } from './dtos/reward-catalogue.dto';
import { GetSelectedProductsResponseDto } from './dtos/get-selected-products-response.dto';
import { UpdateSelectedProductsResponseDto } from './dtos/update-selected-products-response.dto';
import { UpdateSelectedProductsRequestDto } from './dtos/update-selected-products-request.dto';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { Request } from 'express';
import { ProductDetailResponseDto } from './dtos/product-detail-response.dto';
import { UpdateLeaderboardStatusResponseDto } from './dtos/leaderboard-status.dto';
import { UpdateLeaderboardTimelineRequestDto, UpdateLeaderboardTimelineResponseDto } from './dtos/leaderboard-timeline.dto';
import { GetRewardsHistoryResponseDto } from './dtos';


@ApiTags('hr-products')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/catalogues')
export class HrProductsController {  constructor(
    private readonly hrProductsService: HrProductsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'Reward catalogue fetched successfully',
    type: [RewardCatalogueDto],
  })
  @Get()
  @Authority('VIEW_HR_CATALOGUE')
  async getCatalogueForHR(
    @Req() req: Request,
    @Query('search') search?: string,
    @Query('status') status?: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number
  ): Promise<{ data: RewardCatalogueDto[]; total: number }> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.getActiveProductsForHR(user.enterprise.id, search, status, page, limit);
  }

  @ApiResponse({
    status: 200,
    description: 'Product details fetched successfully',
    type: ProductDetailResponseDto,
  })
  @Get('product/:id')
  @Authority('VIEW_HR_PRODUCT_DETAILS')
  async getProductById(
    @Param('id', ParseIntPipe) productId: number
  ): Promise<ProductDetailResponseDto> {
    return this.hrProductsService.getProductDetailResponse(productId);
  }

  @ApiResponse({
    status: 200,
    description: 'Selected products updated successfully',
    type: [UpdateSelectedProductsResponseDto],
  })
  @Put('selected-products')
  @Authority('UPDATE_HR_SELECTED_PRODUCTS')
  async updateSelectedProducts(
    @Req() req: Request,
    @Body() body: UpdateSelectedProductsRequestDto
  ): Promise<UpdateSelectedProductsResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.updateSelectedProductsResponse(user.enterprise.id, body.selections);
  }

  @ApiResponse({
    status: 200,
    description: 'Selected products fetched successfully',
    type: [GetSelectedProductsResponseDto],
  })
  @Get('selected-products')
  @Authority('LIST_HR_SELECTED_PRODUCTS')
  async getSelectedProductsForHR(
    @Req() req: Request
  ): Promise<GetSelectedProductsResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.getSelectedProductsResponse(user.enterprise.id);
  }

  @Put('leaderboard-status')
  @Authority('SHOW_HR_LEADERBOARD_REWARDS')
  @ApiResponse({ status: 200, type: UpdateLeaderboardStatusResponseDto })
  async updateLeaderboardStatus(
    @Req() req: Request,
    @Body('enabled') enabled: boolean,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.updateLeaderboardStatusResponse(user.enterprise.id, enabled);
  }

  @Get('leaderboard-status')
  @Authority('SHOW_HR_LEADERBOARD_REWARDS')
  @ApiResponse({ 
    status: 200, 
    type: UpdateLeaderboardStatusResponseDto,
    description: 'Leaderboard status retrieved successfully' 
  })
  async getLeaderboardStatus(
    @Req() req: Request
  ): Promise<UpdateLeaderboardStatusResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.getLeaderboardStatus(user.enterprise.id);
  }

  @Put('leaderboard-timeline')
  @Authority('SHOW_HR_LEADERBOARD_RANKERS_ON_TIMELINE')
  @ApiResponse({ status: 200, type: UpdateLeaderboardTimelineResponseDto })
  async updateLeaderboardTimeline(
    @Req() req: Request,
    @Body() body: UpdateLeaderboardTimelineRequestDto
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.updateLeaderboardTimelineResponse(user.enterprise.id, body.startDate, body.endDate);
  }

  @Get('rewards-history')
  @Authority('SHOW_HR_LATEST_REWARDS_HISTORY')
  @ApiResponse({
    status: 200,
    description: 'Rewards history retrieved successfully',
    type: GetRewardsHistoryResponseDto,
  })
  async getRewardsHistory(
    @Req() req: Request,
    @Query('page') page?: number,
    @Query('pageSize') pageSize?: number  ): Promise<GetRewardsHistoryResponseDto> {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrProductsService.getRewardsHistory(user.enterprise.id, page, pageSize);
  }
}