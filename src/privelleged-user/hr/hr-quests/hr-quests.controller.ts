import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Query,
  Req,
  UploadedFiles,
  UseGuards,
  UseInterceptors,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiConsumes,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { HrQuestsService } from './hr-quests.service';
import { ErrorResponse } from 'src/common/responses/errorResponse';
import {
  CreateQuestReqDTO,
  CreateQuestResDTO,
  DeleteQuestResDTO,
} from 'src/quest/quest-dto';
import { AuthGuard } from 'src/security/middleware/authGuard.middleware';
import { FilesInterceptor } from '@nestjs/platform-express';
import { getMulterMediaOptions } from 'src/utils/multer.utils';
import { AllowedMixEntensions } from 'src/utils/allowedExtensions.utils';
import { Request } from 'express';
import { Authority } from 'src/security/middleware/authority.decorator';
import { UserProfileService } from 'src/user/user-profile/user-profile.service';
import { getAllQuestsHRfilterQueriesInterface } from './interfaces';
import { GetAllQuestsHrResDTO } from './hr-quests-dto';
import { GenerateMCQDto, AcceptMCQDto } from './dto/hr-mcq.dto';

@ApiTags('HR-quests')
@ApiBearerAuth()
@UseGuards(AuthGuard)
@Controller('hr/quests')
export class HrQuestsController {
  private readonly logger = new Logger(HrQuestsController.name);

  constructor(
    private readonly hrQuestsService: HrQuestsService,
    private readonly userProfileService: UserProfileService,
  ) {}

  @ApiResponse({
    status: 200,
    description: 'HR Get All Quest In An Enterprise',
    type: GetAllQuestsHrResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'The page number of the quests list.',
    type: String,
  })
  @ApiQuery({
    name: 'title',
    required: false,
    description: 'The title filter for the quests list.',
    type: String,
  })
  @Get()
  @Authority('LIST_ENTERPRISE_QUESTS')
  async GetAllEnterpriseQuests(
    @Req() req: Request,
    @Query() queryFilter: getAllQuestsHRfilterQueriesInterface,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrQuestsService.getAllEnterpriseQuests(user, queryFilter);
  }

  @ApiResponse({
    status: 200,
    description: 'HR Create Quest In An Enterprise',
    type: CreateQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @ApiConsumes('multipart/form-data')
  @Post()
  @Authority('CREATE_ENTERPRISE_QUEST')
  @UseInterceptors(
    FilesInterceptor(
      'quest_media',
      3,
      getMulterMediaOptions({
        fileSize: 50,
        fileExtensions: AllowedMixEntensions,
      }),
    ),
  )
  async CreateSingleEnterpriseQuest(
    @Req() req: Request,
    @Body() questData: CreateQuestReqDTO,
    @UploadedFiles() quest_media: Express.Multer.File[],
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrQuestsService.createEnterpriseQuest(
      user,
      questData,
      quest_media,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Generate MCQs from content',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('mcq/generate')
  @Authority('CREATE_ENTERPRISE_QUEST')
  async GenerateMCQs(
    @Req() req: Request,
    @Body() generateMCQDto: GenerateMCQDto,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrQuestsService.generateMCQs(
      user,
      generateMCQDto.content,
      generateMCQDto.difficulty,
      generateMCQDto.numQuestions,
    );
  }

  @ApiResponse({
    status: 200,
    description: 'Accept and save generated MCQs',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('mcq/accept')
  @Authority('CREATE_ENTERPRISE_QUEST')
  async AcceptMCQs(@Req() req: Request, @Body() acceptMCQDto: AcceptMCQDto) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrQuestsService.acceptMCQs(user, acceptMCQDto);
  }

  @ApiResponse({
    status: 200,
    description: 'Reject generated MCQs',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Post('mcq/reject/:questId')
  @Authority('CREATE_ENTERPRISE_QUEST')
  async RejectMCQs(@Req() req: Request, @Param('questId') questId: string) {
    const user = await this.userProfileService.getUserFromToken(req);
    return this.hrQuestsService.rejectMCQs(user, questId);
  }

  @Authority('DELETE_ENTERPRISE_QUEST')
  @ApiResponse({
    status: 200,
    description: 'Delete Quest In An Enterprise',
    type: DeleteQuestResDTO,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Delete('/:questId')
  async DeleteSingleEnterpriseQuest(
    @Req() req: Request,
    @Param('questId') questId: string,
  ) {
    const user = await this.userProfileService.getUserFromToken(req);

    return this.hrQuestsService.deleteEnterpriseQuest(user, questId);
  }

  @ApiResponse({
    status: 200,
    description: 'Get all unique user tags in an enterprise',
    type: Object,
  })
  @ApiResponse({
    status: 404,
    description: 'Error Response',
    type: ErrorResponse,
  })
  @Get('/tags')
  @Authority('MANAGE_TAGS')
  async GetAllUserTags(@Req() req: Request) {
    const user = await this.userProfileService.getUserFromToken(req);
    const tags = await this.userProfileService.getAllUserTags(
      user.enterprise.id,
    );

    return { tags };
  }
}
