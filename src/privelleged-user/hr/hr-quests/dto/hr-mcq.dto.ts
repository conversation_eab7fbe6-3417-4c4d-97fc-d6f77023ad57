import { ApiProperty } from '@nestjs/swagger';
import { <PERSON><PERSON><PERSON>y, IsEnum, IsInt, IsNotEmpty, IsNumber, IsOptional, IsString, Max, Min, MinLength } from 'class-validator';
import { QUEST_DIFFICULTY_TYPES } from 'src/models/quest-entity';

export class GenerateMCQDto {  @ApiProperty({
    description: 'The content from which to generate MCQs',
    example: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit...',
    minLength: 500,
  })
  @IsString()
  @IsNotEmpty()
  @MinLength(500, { message: 'Content must be at least 500 characters long. Please provide more detailed content.' })
  content: string;

  @ApiProperty({
    description: 'Difficulty level for the generated MCQs',
    enum: QUEST_DIFFICULTY_TYPES,
    example: QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
  })
  @IsString()
  @IsNotEmpty()
  difficulty: 'easy' | 'intermediate' | 'hard' | 'very hard';

  @ApiProperty({
    description: 'Number of MCQ questions to generate (min: 5, max: 10)',
    example: 5,
    required: false,
    minimum: 5,
    maximum: 10,
  })
  @IsInt()
  @Min(5)
  @Max(10)
  @IsOptional()
  numQuestions?: number;
}

export class MCQOption {
  @ApiProperty({
    description: 'Option text',
    example: 'This is an option',
  })
  @IsString()
  @IsNotEmpty()
  text: string;
}

export class MCQQuestion {
  @ApiProperty({
    description: 'Question text',
    example: 'What is the main topic of this content?',
  })
  @IsString()
  @IsNotEmpty()
  question: string;

  @ApiProperty({
    description: 'Array of options',
    type: [String],
    example: ['Option 1', 'Option 2', 'Option 3', 'Option 4', 'Option 5'],
  })
  @IsArray()
  options: string[];

  @ApiProperty({
    description: 'Array of indices of correct answers',
    type: [Number],
    example: [0],
  })
  @IsArray()
  correctAnswers: number[];
  
  @ApiProperty({
    description: 'Difficulty level of the question',
    enum: QUEST_DIFFICULTY_TYPES,
    example: QUEST_DIFFICULTY_TYPES.INTERMEDIATE,
  })
  @IsString()
  difficulty: 'easy' | 'intermediate' | 'hard' | 'very hard';
}

export class AcceptMCQDto {
  @ApiProperty({
    description: 'Quest title',
    example: 'Introduction to TypeScript',
    required: false,
  })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({
    description: 'Quest description',
    example: 'Learn the basics of TypeScript with these multiple-choice questions.',
    required: false,
  })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: 'Custom quest name for CUSTOM_QUEST type',
    example: 'My Custom Quest Name',
    required: false,
  })
  @IsString()
  @IsOptional()
  customQuestName?: string;

  @ApiProperty({
    description: 'Quest type ID',
    example: 6,
    required: false,
  })
  @IsOptional()
  questTypeId?: number;
  
  @ApiProperty({
    description: 'Existing quest ID if updating a quest',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsInt()
  questId?: number;
  
  @ApiProperty({
    description: 'Completion credits for the quest',
    example: 150,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  completionCredits?: number;
  
  @ApiProperty({
    description: 'Tags to assign quest to specific users',
    type: [String],
    required: false,
    example: ['onboarding', 'training'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: 'End date of the quest',
    example: '2024-11-13T18:30:00.000Z',
    required: false,
  })
  @IsString()
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    description: 'MCQ questions',
    type: [MCQQuestion],
  })
  @IsArray()
  questions: MCQQuestion[];
}
