import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import {
  AccessTokenEntity,
  ConsumerEmailEntity,
  EnterpriseDomainsEntity,
  EnterpriseEntity,
  PermissionEntity,
  RoleEntity,
  UserEntity,
} from 'src/models/user-entity';
import {
  CommentEntity,
  FeedEntity,
  InteractionEmojiEntity,
  InteractionEntity,
} from 'src/models/feed-entity';
import {
  QuestEntity,
  QuestMediaEntity,
  QuestTypesEntity,
} from 'src/models/quest-entity';
import { DepartmentEntity } from 'src/models/user-entity/department.entity';
import { UserModule } from 'src/user/user.module';
import { LoggerModule } from 'src/common/logger/logger.module';
import { CommonModule } from 'src/common/common.module';
import { HrUtilsService } from './hr-utils.service';
import { S3Service } from 'src/third-party/aws/S3_bucket/s3.service';
import { HrQuestsController } from './hr-quests/hr-quests.controller';
import { HrQuestsService } from './hr-quests/hr-quests.service';
import { HrUsersService } from './hr-users/hr-users.service';
import { HrUsersController } from './hr-users/hr-users.controller';
import { HrFeedController } from './hr-feed/hr-feed.controller';
import { HrFeedService } from './hr-feed/hr-feed.service';
import { HrdepartmentController } from './hr-department/hr-department.controller';
import { HrDepartmentsService } from './hr-department/hr-department.service';
import { ReportEntity } from 'src/models/report-entity';
import { HrReportsController } from './hr-reports/hr-reports.controller';
import { HrReportsService } from './hr-reports/hr-reports.service';
import { HrPermissionsService } from './hr-permissions/hr-permissions.service';
import { HrPermissionsController } from './hr-permissions/hr-permissions.controller';
import { HrAnalyticsController } from './hr-analytics/hr-analytics.controller';
import { HrAnalyticsService } from './hr-analytics/hr-analytics.service';
import { UserCreditsEntity } from 'src/models/credits-entity';
import { LeaderboardEntity } from 'src/models/leaderboard-entity';
import { LeaderboardService } from 'src/leaderboard/leaderboard.service';
import { LeaderboardUtilsService } from 'src/leaderboard/leaderboard-utils.service';
import { HyperLinkEntity } from 'src/models/hyperlinks-entity';
import { HrHyperLinkController } from './hr-hyperlinks/hr-hyperlinks.controller';
import { HrHyperLinkService } from './hr-hyperlinks/hr-hyperlinks.service';
import { HrProductsController } from './/hr-products/hr-products.controller';
import { HrProductsService } from './/hr-products/hr-products.service';
import { ProductsModule } from 'src/products/products.module';
import { ProductEntity } from 'src/models/rewards-entity/product.entity';
import { EnterpriseProductStatusEntity } from 'src/models/rewards-entity/enterpriseProductStatus.entity';
import { MonthlyRewardsHistory } from 'src/models/rewards-entity/MonthlyRewardsHistory.entity';
import { RewardsHistoryService } from './hr-products/services/rewards-history.service';
import { HrEnterpriseController } from './hr-enterprise/hr-enterprise.controller';
import { HrEnterpriseService } from './hr-enterprise/hr-enterprise.service';
import { QuestParticipantEntity } from 'src/models/quest-entity/quest-participants.entity';
import { AIQuestModule } from 'src/quest/AI-quest/AI-quest.module';
import { MCQGenerationService } from 'src/quest/AI-quest/mcq-generation.service';
import { MCQQuestionEntity } from 'src/models/quest-entity/mcq.entity';
import { UserAggregateModule } from './hr-user-analytics/user-analytics.module';
import { UserAggregateController } from './hr-user-analytics/user-analytics.controller';
import { ThirdPartyModule } from 'src/third-party/third-party.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      AccessTokenEntity,
      UserEntity,
      PermissionEntity,
      RoleEntity,
      FeedEntity,
      QuestEntity,
      CommentEntity,
      QuestMediaEntity,
      DepartmentEntity,
      QuestEntity,
      QuestTypesEntity,
      ReportEntity,
      EnterpriseEntity,
      EnterpriseDomainsEntity,
      ConsumerEmailEntity,
      InteractionEmojiEntity,
      InteractionEntity,
      UserCreditsEntity,
      LeaderboardEntity,
      UserCreditsEntity,
      HyperLinkEntity,
      ProductEntity,
      EnterpriseProductStatusEntity,
      MonthlyRewardsHistory,
      QuestParticipantEntity,
      MCQQuestionEntity,
    ]),
    UserModule,
    LoggerModule,
    CommonModule,
    ProductsModule,
    AIQuestModule,
    UserAggregateModule,
    ThirdPartyModule,
  ],
  controllers: [
    HrQuestsController,
    HrUsersController,
    HrFeedController,
    HrdepartmentController,
    HrReportsController,
    HrPermissionsController,
    HrAnalyticsController,
    HrHyperLinkController,
    HrProductsController,
    HrEnterpriseController,
    UserAggregateController,
  ],
  providers: [
    HrUtilsService,
    S3Service,
    HrQuestsService,
    HrUsersService,
    HrFeedService,
    HrDepartmentsService,
    HrReportsService,
    HrPermissionsService,
    HrAnalyticsService,
    LeaderboardService,
    LeaderboardUtilsService,
    HrHyperLinkService,
    HrProductsService,
    RewardsHistoryService,
    HrEnterpriseService,
    MCQGenerationService,
  ],
})
export class HRModule {}
