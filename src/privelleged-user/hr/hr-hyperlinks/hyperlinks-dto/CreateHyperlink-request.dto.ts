import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUrl, IsOptional, IsString, IsArray } from 'class-validator';

export class CreateHyperlinkReqDto {
  @ApiProperty({
    example: 'url',
  })
  @IsUrl({}, { message: 'Invalid URL format' })
  @IsNotEmpty({ message: 'Please provide url for hyperlink' })
  url: string;

  @ApiProperty({
    example: 'description',
  })
  @IsString()
  @IsNotEmpty({ message: 'Please provide label for hyperlink' })
  label: string;

  @ApiProperty({
    example: ['Employee', 'Manager'],
    description: 'Tags to assign to the hyperlink',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
