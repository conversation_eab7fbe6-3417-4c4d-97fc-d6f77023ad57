import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDate,
  IsNotEmpty,
  IsNumber,
  IsString,
  IsUrl,
} from 'class-validator';
import { HyperLinkEntity } from 'src/models/hyperlinks-entity';

export class hyperlinkDto {
  @ApiProperty({
    description: 'The id of the hyperlink',
    example: 1,
  })
  @IsNotEmpty()
  @IsNumber()
  id: number;

  @ApiProperty({
    description: 'The URL of the hyperlink',
    example: 'https://example.com',
  })
  @IsNotEmpty()
  @IsUrl({}, { message: 'URL must be a valid URL' })
  url: string;

  @ApiProperty({
    description: 'The label for the hyperlink',
    example: 'Example Website',
  })
  @IsNotEmpty()
  @IsString()
  label: string;

  @ApiProperty({
    description: 'The created date of the hyperlink',
    example: 'date',
  })
  @IsNotEmpty()
  @IsDate()
  createdAt: Date;

  @ApiProperty({
    description: 'Indicates whether the hyperlink is deleted',
    example: false,
    default: false,
  })
  @IsBoolean()
  isDeleted: boolean;

  @ApiProperty({
    description: 'Tags assigned to the hyperlink',
    example: ['Employee', 'Manager'],
    required: false,
  })
  tags?: string[];

  static transform(object: HyperLinkEntity): hyperlinkDto {
    const transformedObj = new hyperlinkDto();
    transformedObj.id = object.id;
    transformedObj.label = object.label;
    transformedObj.url = object.url;
    transformedObj.createdAt = object.createdAt;
    transformedObj.isDeleted = object.isDeleted;
    transformedObj.tags = object.tags;

    return transformedObj;
  }
}
