import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsUrl, IsOptional, IsString, IsArray } from 'class-validator';

export class UpdateHyperlinkReqDto {
  @ApiProperty({
    example: 'url',
  })
  @IsOptional()
  @IsUrl({}, { message: 'Invalid URL format' })
  url: string;

  @ApiProperty({
    example: 'description',
  })
  @IsOptional()
  @IsString()
  label: string;

  @ApiProperty({
    example: ['Employee', 'Manager'],
    description: 'Tags to assign to the hyperlink',
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];
}
