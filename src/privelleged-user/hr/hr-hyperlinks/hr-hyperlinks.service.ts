import {
  Injectable,
  ConflictException,
  BadRequestException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import {
  CreateHyperlinkReqDto,
  CreateHyperlinkResDto,
  DeleteHyperLinkResDTO,
  GetAllHyperlinksResDTO,
  hyperlinkDto,
  UpdateHyperlinkReqDto,
  UpdateHyperLinkResDTO,
} from './hyperlinks-dto';
import { HyperLinkEntity } from 'src/models/hyperlinks-entity';
import { UserEntity } from 'src/models/user-entity';
import { HrUtilsService } from '../hr-utils.service';

@Injectable()
export class HrHyperLinkService {
  constructor(
    private readonly hrUtilsService: HrUtilsService,

    @InjectRepository(HyperLinkEntity)
    private hyperlinkRepo: Repository<HyperLinkEntity>,
  ) {}

  async getAllHyperlinks(user: UserEntity): Promise<GetAllHyperlinksResDTO> {
    const hyperlinks = await this.hyperlinkRepo.find({
      where: {
        enterprise: { id: user.enterprise.id },
        isDeleted: false,
      },
    });

    // HR users see all hyperlinks without tag filtering
    const resp = hyperlinks.map((item) => hyperlinkDto.transform(item));

    return {
      error: false,
      hyperlinks: resp,
    };
  }

  async createHyperlink(
    user: UserEntity,
    linkData: CreateHyperlinkReqDto,
  ): Promise<CreateHyperlinkResDto> {
    const hyperLinks = await this.hyperlinkRepo.find({
      where: { isDeleted: false, enterprise: { id: user.enterprise.id } },
    });

    if (hyperLinks.length >= 5) {
      throw new BadRequestException(
        'Hyperlinks Limit Exceed : Cannot have more than 5 hyperlinks',
      );
    }

    const { label, url, tags } = linkData;

    const new_Hyperlink = await this.hyperlinkRepo.save({
      enterprise: user.enterprise,
      isDeleted: false,
      label,
      url,
      tags: tags || [],
    });

    const resp = hyperlinkDto.transform(new_Hyperlink);

    return {
      error: false,
      msg: 'Hyperlink created successfully !!',
      hyperlink: resp,
    };
  }

  async deleteHyperlink(
    hyperLinkId: string,
    user: UserEntity,
  ): Promise<DeleteHyperLinkResDTO> {
    const id = this.hrUtilsService.validateAndGetId(hyperLinkId);

    let existing_link = await this.hyperlinkRepo.findOne({
      where: { id, isDeleted: false, enterprise: { id: user.enterprise.id } },
    });

    if (!existing_link) {
      throw new BadRequestException('Hyperlink not found !!');
    }

    existing_link.isDeleted = true;

    existing_link = await this.hyperlinkRepo.save(existing_link);

    const resp = hyperlinkDto.transform(existing_link);

    return {
      error: false,
      msg: 'Hyperlink Deleted Successfully',
      hyperlink: resp,
    };
  }

  async updateHyperlink(
    hyperLinkId: string,
    user: UserEntity,
    updateData: UpdateHyperlinkReqDto,
  ): Promise<UpdateHyperLinkResDTO> {
    const { label, url, tags } = updateData;

    const id = this.hrUtilsService.validateAndGetId(hyperLinkId);

    let existing_link = await this.hyperlinkRepo.findOne({
      where: { id, isDeleted: false, enterprise: { id: user.enterprise.id } },
    });

    if (!existing_link) {
      throw new BadRequestException('Hyperlink not found !!');
    }

    if (label) {
      existing_link.label = label;
    }

    if (url) {
      existing_link.url = url;
    }

    if (tags !== undefined) {
      existing_link.tags = tags;
    }

    existing_link = await this.hyperlinkRepo.save(existing_link);

    const resp = hyperlinkDto.transform(existing_link);

    return {
      error: false,
      msg: 'Hyperlink Updated Successfully',
      hyperlink: resp,
    };
  }
}
