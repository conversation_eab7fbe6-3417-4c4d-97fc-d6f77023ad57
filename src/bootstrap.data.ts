import { PermissionCategory } from './models/user-entity';
import { ROLE_VALUES } from './models/user-entity/role.entity';

const permissionsData = {
  [PermissionCategory.ROLE]: [],

  [PermissionCategory.PERMISSION]: [
    {
      name: 'List HR Permissions Routes',
      value: 'LIST_HR_PERMISSIONS_ROUTES',
      description:
        'User having this authority can view all routes related to HR permissions within the enterprise.',
    },
    {
      name: 'List Admin Permissions Routes',
      value: 'LIST_ADMIN_PERMISSIONS_ROUTES',
      description:
        'User having this authority can view all routes related to administrative permissions within the enterprise.',
    },

    {
      name: 'Access HR Dashboard',
      value: 'ACCESS_HR_DASHBOARD',
      description:
        'User having this authority can access the HR dashboard to manage and monitor HR-related activities.',
    },
    {
      name: 'Exit HR Dashboard',
      value: 'EXIT_HR_DASHBOARD',
      description:
        'User having this authority can safely exit the HR dashboard, ensuring all processes are properly closed.',
    },

    {
      name: 'View HR Analytics',
      value: 'VIEW_HR_ANALYTICS',
      description:
        'User having this authority can access and view the HR analytics data, including user interactions, quests, and feeds statistics.',
    },

    {
      name: 'View Admin Analytics',
      value: 'VIEW_ADMIN_ANALYTICS',
      description:
        'User having this authority can access and view the administrative analytics, including data related to enterprises and other administrative metrics.',
    },
  ],

  [PermissionCategory.REWARDS]: [
    {
      name: 'Create Product',
      value: 'CREATE_PRODUCT',
      description: 'User having this authority can create products.',
      category: 'rewards',
    },
    {
      name: 'Update Product',
      value: 'UPDATE_PRODUCT',
      description: 'User having this authority can update products.',
      category: 'rewards',
    },
    {
      name: 'Delete Product',
      value: 'DELETE_PRODUCT',
      description: 'User having this authority can delete products.',
      category: 'rewards',
    },
    {
      name: 'Toggle Product Availability',
      value: 'TOGGLE_PRODUCT_AVAILABILITY',
      description:
        'User having this authority can toggle product availability.',
      category: 'rewards',
    },
    {
      name: 'Get Enterprise Products',
      value: 'GET_ENTERPRISE_PRODUCTS',
      description: 'User having this authority can view enterprise products.',
      category: 'rewards',
    },
    {
      name: 'Toggle Enterprise Product',
      value: 'TOGGLE_ENTERPRISE_PRODUCT',
      description:
        'User having this authority can toggle enterprise product visibility.',
      category: 'rewards',
    },
    {
      name: 'Toggle Enterprise Rewards',
      value: 'TOGGLE_ENTERPRISE_REWARDS',
      description: 'User having this authority can toggle enterprise rewards.',
      category: 'rewards',
    },
    {
      name: 'View Product Details',
      value: 'VIEW_PRODUCT_DETAILS',
      description: 'User having this authority can view product details.',
      category: 'rewards',
    },
    {
      name: 'View HR Catalogue',
      value: 'VIEW_HR_CATALOGUE',
      description:
        'User having this authority can view the HR product catalogue.',
      category: 'rewards',
    },
    {
      name: 'View HR Product Details',
      value: 'VIEW_HR_PRODUCT_DETAILS',
      description:
        'User having this authority can view detailed information about HR products.',
      category: 'rewards',
    },
    {
      name: 'Update HR Selected Products',
      value: 'UPDATE_HR_SELECTED_PRODUCTS',
      description:
        'User having this authority can update the selection of HR products.',
      category: 'rewards',
    },
    {
      name: 'List HR Selected Products',
      value: 'LIST_HR_SELECTED_PRODUCTS',
      description:
        'User having this authority can list the selected HR products.',
      category: 'rewards',
    },
    {
      name: 'Show HR Leaderboard Rewards',
      value: 'SHOW_HR_LEADERBOARD_REWARDS',
      description:
        'User having this authority can show the rewards on leaderboard.',
      category: 'rewards',
    },
    {
      name: 'Show HR Leaderboard Rankers on Timeline',
      value: 'SHOW_HR_LEADERBOARD_RANKERS_ON_TIMELINE',
      description:
        'User having this authority can show the Show Leaderboard Rankers on Timeline.',
      category: 'rewards',
    },
    {
      name: 'Show HR Latest Rewards History',
      value: 'SHOW_HR_LATEST_REWARDS_HISTORY',
      description:
        'User having this authority can view the latest rewards history data.',
      category: 'rewards',
    },
  ],

  [PermissionCategory.ENTERPRISE]: [
    {
      name: 'List Enterprise',
      value: 'LIST_ENTERPRISE',
      description: 'User having this authority can list enterprises',
    },
    {
      name: 'Create Enterprise',
      value: 'CREATE_ENTERPRISE',
      description: 'User having this authority can create a new enterprise',
    },
    {
      name: 'View Enterprise',
      value: 'VIEW_ENTERPRISE',
      description: 'User having this authority can view an enterprise',
    },
    {
      name: 'Update Enterprise',
      value: 'UPDATE_ENTERPRISE',
      description: 'User having this authority can update an enterprise',
    },
    {
      name: 'Delete Enterprise',
      value: 'DELETE_ENTERPRISE',
      description: 'User having this authority can delete an enterprise',
    },
    {
      name: 'Update Enterprise Thumbnail',
      value: 'UPDATE_ENTERPRISE_THUMBNAIL',
      description:
        'User having this authority can update the enterprise thumbnail',
    },
    {
      name: 'Get Enterprise Details',
      value: 'GET_ENTERPRISE_DETAILS',
      description: 'User having this authority can get the enterprise details',
    },
  ],

  [PermissionCategory.ENTERPRISE_DOMAIN]: [
    {
      name: 'List Enterprise Domain',
      value: 'LIST_ENTERPRISE_DOMAIN',
      description: 'User having this authority can list enterprise domains',
    },
    {
      name: 'View Enterprise Domain',
      value: 'VIEW_ENTERPRISE_DOMAIN',
      description: 'User having this authority can view an enterprise domain',
    },
    {
      name: 'Update Enterprise Domain',
      value: 'UPDATE_ENTERPRISE_DOMAIN',
      description: 'User having this authority can update an enterprise domain',
    },
  ],

  [PermissionCategory.USERS]: [
    {
      name: 'Delete User',
      value: 'DELETE_USER',
      description:
        "User having this authority can delete another user's account",
    },
    {
      name: 'Update User',
      value: 'UPDATE_USER',
      description: "User having this authority can update another user's data",
    },
    {
      name: 'Add Single User',
      value: 'ADD_SINGLE_USER',
      description:
        'User having this authority can add a new user to their enterprise.',
    },
    {
      name: 'Add Bulk User',
      value: 'ADD_BULK_USERS',
      description:
        'User having this authority can add users in bulk via CSV file to their enterprise.',
    },

    {
      name: 'Get All Users List',
      value: 'GET_ALL_USERS_LIST',
      description:
        'User having this authority can retrieve the list of all users in the enterprise.',
    },
    {
      name: 'Get Single User Profile',
      value: 'GET_SINGLE_USER_PROFILE',
      description:
        'User having this authority can view the profile details of a single user in the enterprise.',
    },
    {
      name: 'Manage Tags',
      value: 'MANAGE_TAGS',
      description:
        'User having this authority can create, edit, assign, and remove tags for users within the enterprise.',
    },

    {
      name: 'Get Single User Quest Metrics Historys',
      value: 'GET_SINGLE_USER_QUEST_METRICS_HISTORY',
      description:
        'User having this authority can view the profile details of a single user in the enterprise.',
    },
    {
      name: 'View User Data',
      value: 'VIEW_USER_DATA',
      description:
        'User having this authority can view detailed user data including quest participation and performance metrics',
    },
  ],

  [PermissionCategory.FEED]: [
    {
      name: 'List Enterprise Feeds',
      value: 'LIST_ENTERPRISE_FEEDS',
      description:
        'User having this authority can view all feeds related to the enterprise.',
    },
    {
      name: 'Delete Feed Enterprise',
      value: 'DELETE_ENTERPRISE_FEED',
      description:
        'User having this authority can delete any feed related to the enterprise.',
    },
    {
      name: 'Delete Comment',
      value: 'DELETE_FEED_COMMENT',
      description:
        'User having this authority can delete a comment on the feed.',
    },
  ],

  [PermissionCategory.QUEST]: [
    {
      name: 'List Enterprise Quests',
      value: 'LIST_ENTERPRISE_QUESTS',
      description:
        'User having this authority can view all quests related to the enterprise.',
    },
    {
      name: 'Create Quest',
      value: 'CREATE_ENTERPRISE_QUEST',
      description:
        'User having this authority can create new enterprise quests.',
    },
    {
      name: 'Delete Quest',
      value: 'DELETE_ENTERPRISE_QUEST',
      description: 'User having this authority can delete enterprise quests.',
    },
  ],

  [PermissionCategory.DEPARTMENT]: [
    {
      name: 'Create Department',
      value: 'CREATE_DEPARTMENT',
      description:
        'User having this authority can create new enterprise departments.',
    },
    {
      name: 'List Department',
      value: 'LIST_DEPARTMENT',
      description:
        'User having this authority can view the list of enterprise departments.',
    },
    {
      name: 'View Department',
      value: 'VIEW_DEPARTMENT',
      description:
        'User having this authority can view the details of an enterprise department.',
    },
    {
      name: 'Update Department',
      value: 'UPDATE_DEPARTMENT',
      description:
        'User having this authority can update the details of an enterprise department.',
    },
    {
      name: 'Delete Department',
      value: 'DELETE_DEPARTMENT',
      description:
        'User having this authority can delete an enterprise department.',
    },
  ],

  [PermissionCategory.REPORT]: [
    {
      name: 'List Feed Reports',
      value: 'LIST_FEED_REPORTS',
      description:
        'User having this authority can view all reports related to feeds.',
    },
    {
      name: 'List Quest Reports',
      value: 'LIST_ENTERPRISE_QUEST_REPORTS',
      description:
        'User having this authority can view all reports related to enterprise quests.',
    },
    {
      name: 'Review Feed Report',
      value: 'REVIEW_REPORT',
      description:
        'User having this authority can review and take action on any reports.',
    },
  ],

  [PermissionCategory.HYPERLINK]: [
    {
      name: 'List Hyperlinks',
      value: 'LIST_HYPERLINKS',
      description: 'User having this authority can view all hyperlinks.',
    },
    {
      name: 'Create Hyperlinks',
      value: 'CREATE_HYPERLINKS',
      description: 'User having this authority can create hyperlinks.',
    },
    {
      name: 'Delete Hyperlinks',
      value: 'DELETE_HYPERLINKS',
      description: 'User having this authority can delete hyperlinks.',
    },
    {
      name: 'Update Hyperlinks',
      value: 'UPDATE_HYPERLINKS',
      description: 'User having this authority can update hyperlinks.',
    },
  ],
};

export const PermissionsList = Object.entries(permissionsData).flatMap(
  ([category, permissions]) =>
    permissions.map((permission) => ({
      ...permission,
      category: category as PermissionCategory,
    })),
);

export const RolesList = [
  {
    name: 'Admin',
    value: ROLE_VALUES.ADMIN,
    description: 'Admin roles',
    permissionValueList: [
      'LIST_ADMIN_PERMISSIONS_ROUTES',
      'ACCESS_HR_DASHBOARD',
      'EXIT_HR_DASHBOARD',
      'VIEW_ADMIN_ANALYTICS',
      'LIST_ENTERPRISE',
      'CREATE_ENTERPRISE',
      'VIEW_ENTERPRISE',
      'UPDATE_ENTERPRISE',
      'DELETE_ENTERPRISE',
      'CREATE_PRODUCT',
      'UPDATE_PRODUCT',
      'DELETE_PRODUCT',
      'TOGGLE_PRODUCT_AVAILABILITY',
      'GET_ENTERPRISE_PRODUCTS',
      'TOGGLE_ENTERPRISE_PRODUCT',
      'TOGGLE_ENTERPRISE_REWARDS',
      'VIEW_PRODUCT_DETAILS',

      'LIST_ENTERPRISE_DOMAIN',
      'VIEW_ENTERPRISE_DOMAIN',
      'UPDATE_ENTERPRISE_DOMAIN',
      'VIEW_USER_DATA', // Add permission to Admin role
    ],
  },

  {
    name: 'HR',
    value: ROLE_VALUES.HR,
    description: 'HR roles',
    permissionValueList: [
      'LIST_HR_PERMISSIONS_ROUTES',
      'VIEW_HR_ANALYTICS',

      'DELETE_USER',
      'UPDATE_USER',
      'ADD_SINGLE_USER',
      'ADD_BULK_USERS',
      'GET_ALL_USERS_LIST',
      'GET_SINGLE_USER_PROFILE',
      'MANAGE_TAGS',
      'GET_SINGLE_USER_QUEST_METRICS_HISTORY',

      'LIST_ENTERPRISE_FEEDS',
      'DELETE_ENTERPRISE_FEED',
      'DELETE_FEED_COMMENT',

      'LIST_FEED_REPORTS',
      'LIST_ENTERPRISE_QUEST_REPORTS',
      'REVIEW_REPORT',

      'LIST_ENTERPRISE_QUESTS',
      'CREATE_ENTERPRISE_QUEST',
      'DELETE_ENTERPRISE_QUEST',

      'LIST_DEPARTMENT',
      'VIEW_DEPARTMENT',
      'CREATE_DEPARTMENT',
      'UPDATE_DEPARTMENT',
      'DELETE_DEPARTMENT',

      'LIST_HYPERLINKS',
      'CREATE_HYPERLINKS',
      'DELETE_HYPERLINKS',
      'UPDATE_HYPERLINKS',

      'UPDATE_ENTERPRISE_THUMBNAIL',
      'GET_ENTERPRISE_DETAILS',

      'VIEW_HR_CATALOGUE',
      'VIEW_HR_PRODUCT_DETAILS',
      'UPDATE_HR_SELECTED_PRODUCTS',
      'LIST_HR_SELECTED_PRODUCTS',
      'SHOW_HR_LEADERBOARD_REWARDS',
      'SHOW_HR_LEADERBOARD_RANKERS_ON_TIMELINE',
      'SHOW_HR_LATEST_REWARDS_HISTORY',

      'VIEW_USER_DATA', // Add permission to HR role
    ],
  },

  {
    name: 'User',
    value: ROLE_VALUES.USER,
    description: 'User roles',
    permissionValueList: [],
  },
];

export const ConsumerEmailList = ['@abc.co', '@abc.in'];

export const InteractionEmojiList = [
  {
    name: 'SMILE EMOJI',
    value: 'SMILE',
    url: 'https://microcosmworkspoc.s3.us-east-1.amazonaws.com/d5b5f0af-0e20-4300-aa7b-63ab4c32e842-smile_emoji.png',
  },
  {
    name: 'LAUGH EMOJI',
    value: 'LAUGH',
    url: 'https://microcosmworkspoc.s3.us-east-1.amazonaws.com/f59a99f9-4090-4994-bd12-138561de811f-laugh_emoji.png',
  },
  {
    name: 'LOVED EMOJI',
    value: 'LOVED',
    url: 'https://microcosmworkspoc.s3.us-east-1.amazonaws.com/dd472a35-6f74-44bb-ba33-6d5d97c77b9a-loved_emoji.png',
  },
  {
    name: 'SAD EMOJI',
    value: 'SAD',
    url: 'https://microcosmworkspoc.s3.us-east-1.amazonaws.com/8d816276-355c-4413-901e-d6a33546fe2d-sad_emoji.png',
  },
  {
    name: 'ANGRY EMOJI',
    value: 'ANGRY',
    url: 'https://microcosmworkspoc.s3.us-east-1.amazonaws.com/b7746198-1d01-46fa-9755-363ffad61e49-angry_emoji.png',
  },
];

export const QuestTypesList = [
  {
    name: 'Sweat Sesh',
    value: 'FITNESS_QUEST', // !!!! if this value gets changed must change update profile code for fitnes quest assignment login to disabled user.
    description:
      'Energize your day with quick, fun physical challenges designed to get you moving.',
  },
  {
    name: 'VidVenture',
    value: 'SHORT_VIDEO_QUEST',
    description:
      'Bring your ideas to life with engaging video quests that showcase your creativity.',
  },
  {
    name: 'SnapDash',
    value: 'PHOTOGRAPHY_QUEST',
    description:
      'Capture your world through creative photo challenges and share your unique perspective.',
  },
  {
    name: 'BrainSphere',
    value: 'PUZZLE_QUEST',
    description:
      'Sharpen your mind with intriguing puzzles that challenge your logic and creativity.',
  },
  {
    name: 'CodeCrafter',
    value: 'CODING_QUEST',
    description:
      'Put your coding skills to the test with fun and challenging programming tasks.',
  },
  {
    name: 'Custom Quest',
    value: 'CUSTOM_QUEST',
    description:
      'Create your own unique quest type with a custom name and description.',
  },
];

export const DepartmentsList = [
  {
    name: 'Human Resources',
    value: 'HUMAN_RESOURCES',
    description:
      'Responsible for recruiting, onboarding, and employee management.',
  },
  {
    name: 'Finance and Accounting',
    value: 'FINANCE_AND_ACCOUNTING',
    description: 'Manages company budgets, payroll, and financial reporting.',
  },
  {
    name: 'Information Technology',
    value: 'INFORMATION_TECHNOLOGY',
    description:
      'Handles IT infrastructure, software development, and cybersecurity.',
  },
];
