import { Injectable } from '@nestjs/common';
import { CustomLogger } from './logger/custom-logger.service';
import { SesService } from 'src/third-party/aws/SES/ses.service';
import { sendTextMailInterface } from './interfaces';
import { PDF_PARSING_FAILURE_ALERT_TEMPLATE } from './email-templates';

@Injectable()
export class EmailService {
  async sendPdfParsingFailureAlert({
    toEmails,
    fromEmail,
    subject,
    errorMessage,
    userId,
    userEmail,
    title,
    uploadTime,
    additionalContext,
    fileName,
    fileSize,
    s3Url,
    enterpriseId,
    enterpriseName,
    attachment,
  }: {
    toEmails: string[];
    fromEmail: string;
    subject: string;
    errorMessage: any;
    userId: number;
    userEmail: string;
    title: string;
    uploadTime: string;
    additionalContext: string;
    fileName: string;
    fileSize: number;
    s3Url?: string;
    enterpriseId?: string | number;
    enterpriseName?: string;
    attachment?: { filename: string; content: <PERSON>uffer<ArrayBufferLike>; };
  }) {
    // Render HTML template
    const html = PDF_PARSING_FAILURE_ALERT_TEMPLATE
      .replace(/{{userEmail}}/g, userEmail)
      .replace(/{{userId}}/g, String(userId))
      .replace(/{{title}}/g, title)
      .replace(/{{uploadTime}}/g, uploadTime)
      .replace(/{{fileName}}/g, fileName)
      .replace(/{{fileSize}}/g, String(fileSize))
      .replace(/{{additionalContext}}/g, additionalContext)
      .replace(/{{s3Url}}/g, s3Url || 'N/A')
      .replace(/{{enterpriseId}}/g, enterpriseId ? String(enterpriseId) : 'N/A')
      .replace(/{{enterpriseName}}/g, enterpriseName || 'N/A')
      .replace(/{{errorMessage}}/g, errorMessage);
    const textBody = `PDF parsing failed.\n\nUser: ${userEmail} (ID: ${userId})\nEnterprise: ${enterpriseName || 'N/A'} (ID: ${enterpriseId || 'N/A'})\nTitle: ${title}\nUpload Time: ${uploadTime}\nFile: ${fileName} (${fileSize} bytes)\nS3 URL: ${s3Url || 'N/A'}\nAdditional Context: ${additionalContext}\n\nError: ${errorMessage}`;
    this.logger.log(
      `PDF Parsing Failure Alert email triggered. To: ${toEmails.join(", ")}, User: ${userEmail} (ID: ${userId}), Enterprise: ${enterpriseName || 'N/A'} (ID: ${enterpriseId || 'N/A'}), File: ${fileName}, S3 URL: ${s3Url || 'N/A'}`,
      
    );
    for (const toEmail of toEmails) {
      await this.sendTextMail({
        toEmail,
        fromEmail,
        subject,
        textBody,
        html,
      });
    }
  }
  constructor(
    private readonly sesService: SesService,
    private readonly logger: CustomLogger,
  ) {}

  async sendTextMail({
    toEmail,
    fromEmail,
    subject,
    textBody,
    html,
  }: sendTextMailInterface) {
    try {
      await this.sesService.sendEmail(
        toEmail,
        fromEmail,
        subject,
        textBody,
        html,
      );
    } catch (error) {
      throw error;
    }
  }

  async sendBatchEmail(data: sendTextMailInterface[]) {
    const results = await Promise.allSettled(
      data.map((item) => this.sendTextMail(item)),
    );

    return results;
  }

  async sendBatchEmailsWithDelay(emailsToSend: any[]) {
    const batchSize = Number(process.env.BULK_EMAIL_BATCH_SIZE) || 50;
    const delay = Number(process.env.BULK_EMAIL_BATCH_DELAY) || 1000;

    const totalBatches = Math.ceil(emailsToSend.length / batchSize);

    for (let batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
      const start = batchIndex * batchSize;
      const end = Math.min((batchIndex + 1) * batchSize, emailsToSend.length);

      const currentBatch = emailsToSend.slice(start, end);

      await this.sendBatchEmail(currentBatch);

      if (batchIndex < totalBatches - 1) {
        await this.delay(delay); // Custom delay function
      }
    }
  }

  // Utility function to delay execution
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
