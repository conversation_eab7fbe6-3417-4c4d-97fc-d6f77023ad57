export const PDF_PARSING_FAILURE_ALERT_TEMPLATE = `<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <title>PDF Parsing Failure Alert</title>
  <style>
    body { font-family: Arial, sans-serif; background: #f8f8f8; color: #222; }
    .container { background: #fff; border-radius: 8px; padding: 24px; margin: 24px auto; max-width: 600px; box-shadow: 0 2px 8px #eee; }
    .header { font-size: 20px; font-weight: bold; color: #b71c1c; margin-bottom: 16px; }
    .section { margin-bottom: 16px; }
    .label { font-weight: bold; }
    .error { color: #b71c1c; font-family: monospace; white-space: pre-wrap; background: #fbe9e7; padding: 12px; border-radius: 4px; }
    .footer { font-size: 12px; color: #888; margin-top: 24px; }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">PDF Parsing Failure Alert</div>
    <div class="section"><span class="label">User:</span> {{userEmail}} (ID: {{userId}})</div>
    <div class="section"><span class="label">Enterprise:</span> {{enterpriseName}} (ID: {{enterpriseId}})</div>
    <div class="section"><span class="label">Title:</span> {{title}}</div>
    <div class="section"><span class="label">Upload Time:</span> {{uploadTime}}</div>
    <div class="section"><span class="label">File:</span> {{fileName}} ({{fileSize}} bytes)</div>
    <div class="section"><span class="label">S3 URL:</span> {{s3Url}}</div>
    <div class="section"><span class="label">Additional Context:</span> {{additionalContext}}</div>
    <div class="section"><span class="label">Error:</span>
      <div class="error">{{errorMessage}}</div>
    </div>
    <div class="footer">This is an automated alert from Thrivify PDF Parsing System.</div>
  </div>
</body>
</html>`;
