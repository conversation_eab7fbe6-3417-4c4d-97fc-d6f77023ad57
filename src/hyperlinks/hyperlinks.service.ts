import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { HyperLinkEntity } from 'src/models/hyperlinks-entity';
import { UserEntity } from 'src/models/user-entity';
import {
  GetAllHyperlinksResDTO,
  hyperlinkDto,
} from 'src/privelleged-user/hr/hr-hyperlinks/hyperlinks-dto';
import { Repository } from 'typeorm';

@Injectable()
export class HyperlinksService {
  constructor(
    @InjectRepository(HyperLinkEntity)
    private hyperlinkRepo: Repository<HyperLinkEntity>,
  ) {}

  async getAllHyperlinks(user: UserEntity): Promise<GetAllHyperlinksResDTO> {
    const hyperlinks = await this.hyperlinkRepo.find({
      where: {
        enterprise: { id: user.enterprise.id },
        isDeleted: false,
      },
    });

    // Filter hyperlinks based on tags for regular users
    const filteredHyperlinks = hyperlinks.filter(hyperlink => {
      // If hyperlink has no tags, it's available to everyone
      if (!hyperlink.tags || hyperlink.tags.length === 0) return true;
      
      // If hyperlink has tags, user must have at least one matching tag
      // If user has no tags, they cannot see tagged hyperlinks
      return user.tags && hyperlink.tags.some(tag => user.tags.includes(tag));
    });

    const resp = filteredHyperlinks.map((item) => hyperlinkDto.transform(item));

    return {
      error: false,
      hyperlinks: resp,
    };
  }
}
