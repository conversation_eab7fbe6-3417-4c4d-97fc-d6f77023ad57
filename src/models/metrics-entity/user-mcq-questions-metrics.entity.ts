import {
  <PERSON>um<PERSON>,
  CreateDate<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>To<PERSON>ne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { MCQQuestionEntity } from '../quest-entity/mcq.entity';
import { UserQuestMetricsEntity } from './user-quest-metrics.entity';

@Entity('user_mcq_question_metrics')
export class UserMCQQuestionMetricsEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => MCQQuestionEntity, (question) => question.id, {
    nullable: false,
  })
  @JoinColumn({ name: 'question_id' })
  question: MCQQuestionEntity;

  @Column({ type: 'text', nullable: false })
  aISuggestion: string;

  @Column({ type: 'text', nullable: false })
  improvementNeeded: string;

  @Column({ type: 'integer', nullable: false })
  analysisScore: number;

  @Column({ type: 'text', nullable: true })
  answer: number[];

  @ManyToOne(
    () => UserQuestMetricsEntity,
    (userQuestMetrics) => userQuestMetrics.userMCQQuestionMetrics,
  )
  @JoinColumn({ name: 'user_quest_metrics_id' })
  userQuestMetrics: UserQuestMetricsEntity;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;
}
