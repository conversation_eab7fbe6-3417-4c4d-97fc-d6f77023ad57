import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Unique,
} from 'typeorm';
import { QUEST_DIFFICULTY_TYPES, SUBMISSION_MEDIA_TYPES } from './quest.entity';

@Entity('codecrafter_quest_pool')
export class CodeCrafterQuestPoolEntity {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({
    type: 'enum',
    enum: QUEST_DIFFICULTY_TYPES,
    nullable: false,
  })
  difficulty: QUEST_DIFFICULTY_TYPES;

  @Column({ type: 'varchar', length: 255, nullable: false })
  title: string;

  @Column({ type: 'text', nullable: false })
  description: string;

  @Column({
    type: 'enum',
    enum: SUBMISSION_MEDIA_TYPES,
    nullable: false,
  })
  proofOfCompletion: SUBMISSION_MEDIA_TYPES;

  @Column({ type: 'boolean', default: false, nullable: false })
  assigned: boolean;

  @CreateDateColumn({ nullable: false })
  createdAt: Date;

  @UpdateDateColumn({ nullable: false })
  updatedAt: Date;
}