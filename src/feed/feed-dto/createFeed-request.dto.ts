import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsOptional, IsString } from 'class-validator';

export class CreateFeedReqDTO {
  @ApiProperty({
    example: 'sample caption',
    description: 'feed caption',
  })
  captionText?: string;

  @ApiProperty({
    type: 'string',
    format: 'binary[]',
    example: 'Feed Media []',
    description: 'Feed Media [ ]',
    required: false,
  })
  readonly feed_media?: any;

    @ApiProperty({
    description: 'List of embed video URLs (YouTube, Vimeo, etc)',
    example: ['https://www.youtube.com/watch?v=xyz123'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  embedUrls?: string[];
}