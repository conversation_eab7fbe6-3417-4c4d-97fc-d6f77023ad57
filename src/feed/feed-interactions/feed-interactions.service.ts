import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  CommentEntity,
  EmojiCountEntity,
  FeedEntity,
  InteractionEmojiEntity,
  InteractionEntity,
} from 'src/models/feed-entity';
import { UserEntity } from 'src/models/user-entity';
import { DataSource, Repository } from 'typeorm';
import { FeedService } from '../feed.service';
import {
  CreateCommentReqDTO,
  FeedCommentDTO,
  GetAllCommentsOnFeedResDTO,
  InteractOnFeedReqDTO,
  CreateCommentOnFeedResDTO,
  InteractOnFeedResDTO,
  InteractionDTO,
  DeleteCommentOnFeedResDTO,
  GetAllInteracttionsOnFeedResDTO,
} from './feed-interactions-dto';
import {
  getAllCommentsFilterQueryInterface,
  listFeedInteractionsFilterQueryInterface,
} from './interfaces';
import {
  EmojiCountDTO,
  GetFeedInteractionsCountResDTO,
} from 'src/privelleged-user/hr/hr-feed/hr-feed-dto/GetFeedInteractionsCount-response.dto';
import { FeedEmojiCountDTO } from '../feed-dto/feed.dto';

@Injectable()
export class FeedInteractionsService {
  constructor(
    private readonly feedService: FeedService,
    private readonly dataSource: DataSource,

    @InjectRepository(FeedEntity)
    private readonly feedRepo: Repository<FeedEntity>,

    @InjectRepository(UserEntity)
    private readonly userRepo: Repository<UserEntity>,

    @InjectRepository(CommentEntity)
    private readonly CommentRepo: Repository<CommentEntity>,

    @InjectRepository(InteractionEmojiEntity)
    private readonly EmojiRepo: Repository<InteractionEmojiEntity>,

    @InjectRepository(InteractionEntity)
    private readonly InteractionRepo: Repository<InteractionEntity>,

    @InjectRepository(EmojiCountEntity)
    private readonly emojiCountRepo: Repository<EmojiCountEntity>,
  ) {}

  async getAllCommentsOnFeed(
    user: UserEntity,
    feedId: string,
    filterQueries: getAllCommentsFilterQueryInterface,
  ): Promise<GetAllCommentsOnFeedResDTO> {
    const feed = await this.checkAndGetFeed(user, feedId);

    const { page, limit } = filterQueries;
    const pageNumber = Math.max(parseInt(page, 10) || 1, 1);
    const take = Math.max(parseInt(limit, 10) || 10, 1);
    const offset = (pageNumber - 1) * take;

    const [commentsData, total] = await this.CommentRepo.createQueryBuilder(
      'comment',
    )
      .leftJoinAndSelect('comment.author', 'author')
      .where('comment.feed = :feedId', { feedId: feed.id })
      .andWhere('comment.isDeleted = :isDeleted', { isDeleted: false })
      .orderBy('comment.createdAt', 'DESC')
      .skip(offset)
      .take(take)
      .getManyAndCount();

    const comments = commentsData.map((item) => FeedCommentDTO.transform(item));

    return { error: false, total, nbHits: comments.length, comments };
  }

  async createComment(
    user: UserEntity,
    feedId: string,
    feedData: CreateCommentReqDTO,
  ): Promise<CreateCommentOnFeedResDTO> {
    const { content } = feedData;
    const feed = await this.checkAndGetFeed(user, feedId);

    if (!(await this.canInteractOnFeed(user, feed))) {
      throw new UnauthorizedException(
        "Become the feed's author friend to interact on this feed.",
      );
    }

    let comment = new CommentEntity();
    comment.content = content;

    comment.feed = feed;
    comment.author = user;

    comment = await this.CommentRepo.save(comment);

    feed.numOfComments = feed.numOfComments + 1;
    feed.comments.push(comment);

    await this.feedRepo.save(feed);
    await this.userRepo.save(user);

    const commentResp = FeedCommentDTO.transform(comment);

    return {
      error: false,
      msg: 'comment posted successfully !',
      comment: commentResp,
    };
  }

  async deleteComment(
    user: UserEntity,
    commentId: string,
  ): Promise<DeleteCommentOnFeedResDTO> {
    const id = this.feedService.validateAndGetFeedId(commentId);

    let comment = await this.CommentRepo.findOne({
      where: { id: id },
      relations: ['author'],
    });

    if (!comment) {
      throw new BadRequestException('Comment not found !!');
    }

    if (comment.author.id != user.id) {
      throw new UnauthorizedException(
        'This account is not authorized to delete this comment.',
      );
    }

    comment.isDeleted = true;
    comment = await this.CommentRepo.save(comment);

    const commentResp = FeedCommentDTO.transform(comment);

    return {
      error: false,
      msg: 'Comment deleted successfully !!',
      comment: commentResp,
    };
  }

  async InteractOnFeed(
    user: UserEntity,
    feedId: string,
    interactionData: InteractOnFeedReqDTO,
  ): Promise<InteractOnFeedResDTO> {
    try {
      const feed = await this.checkAndGetFeed(user, feedId);

      const { emojiId } = interactionData;
      const id = this.feedService.validateAndGetFeedId(emojiId); // Ensure valid emojiId

      // Check if emoji exists
      const interactionEmoji = await this.EmojiRepo.findOne({ where: { id } });
      if (!interactionEmoji) {
        throw new BadRequestException('Emoji with this Id not found.');
      }

      // Check for existing interaction from the user
      const existingInteraction = await this.InteractionRepo.findOne({
        where: { feed: { id: feed.id }, user: { id: user.id } },
        relations: ['emoji', 'user', 'feed'],
      });

      let msg: string;
      let interaction = existingInteraction || new InteractionEntity();

      // Handle new interaction or update existing one
      if (!existingInteraction) {
        interaction.emoji = interactionEmoji;
        interaction.user = user;
        interaction.feed = feed;
        feed.interactions.push(interaction); // Update feed's interactions
        msg = 'Created new interaction';

        // Increment emoji count
        await this.UpdateEmojiCount(feed, interactionEmoji, 'increment');
      } else {
        // Renew or delete existing interaction
        if (interaction.isDeleted) {
          interaction.isDeleted = false;
          interaction.emoji = interactionEmoji;
          msg = 'Renewed and updated old interaction';

          // Increment emoji count for the renewed interaction
          await this.UpdateEmojiCount(feed, interactionEmoji, 'increment');
        } else if (interaction.emoji.id === interactionEmoji.id) {
          interaction.isDeleted = true;
          msg = 'Deleted old interaction';

          // Decrement emoji count for the deleted interaction
          await this.UpdateEmojiCount(feed, interaction.emoji, 'decrement');
        } else {
          // Update emoji if it's changed
          await this.UpdateEmojiCount(feed, interaction.emoji, 'decrement'); // Decrement count for the old emoji
          interaction.emoji = interactionEmoji;
          msg = 'Updated old interaction';

          // Increment emoji count for the new emoji
          await this.UpdateEmojiCount(feed, interactionEmoji, 'increment');
        }
      }

      // Save the interaction and feed in a transaction for atomicity
      await this.dataSource.transaction(async (manager) => {
        interaction = await manager.save(InteractionEntity, interaction); // Save interaction
        await manager.save(FeedEntity, feed); // Update feed interactions
      });

      // Return the response
      const feedEmojiCounts = await this.emojiCountRepo.find({
        where: {
          feed: { id: feed.id },
        },
        relations: ['emoji'],
      });

      const emojiCountResp = feedEmojiCounts.map((item) =>
        FeedEmojiCountDTO.transform(item),
      );

      return {
        error: false,
        msg,
        emojiCounts: emojiCountResp,
      };
    } catch (error) {
      throw error;
    }
  }

  async UpdateEmojiCount(
    feed: FeedEntity,
    emoji: InteractionEmojiEntity,
    action: string,
  ) {
    try {
      const emojiCountEntry = await this.emojiCountRepo.findOne({
        where: { feed: { id: feed.id }, emoji: { id: emoji.id } },
      });

      if (!emojiCountEntry) {
        // If no entry exists, create one with the initial count set to 1 or 0 based on the action
        const newEntry = new EmojiCountEntity();
        newEntry.feed = feed;
        newEntry.emoji = emoji;
        newEntry.count = action === 'increment' ? 1 : 0;
        await this.emojiCountRepo.save(newEntry);
      } else {
        // Update the count based on the action
        if (action === 'increment') {
          emojiCountEntry.count++;
        } else if (action === 'decrement' && emojiCountEntry.count > 0) {
          emojiCountEntry.count--;
        }
        await this.emojiCountRepo.save(emojiCountEntry);
      }
    } catch (error) {
      throw error;
    }
  }

  private async checkAndGetFeed(
    user: UserEntity,
    feedId: string,
  ): Promise<FeedEntity> {
    const id = this.feedService.validateAndGetFeedId(feedId);

    const feed = await this.feedRepo.findOne({
      where: {
        id: id,
      },
      relations: [
        'enterprise',
        'author',
        'comments',
        'comments.author',
        'interactions',
      ],
      order: {
        comments: {
          createdAt: 'DESC',
        },
      },
    });

    if (!feed || feed.isDeleted === true) {
      throw new BadRequestException('feed not found !!');
    }

    return feed;
  }

  private async canInteractOnFeed(
    user: UserEntity,
    feed: FeedEntity,
  ): Promise<boolean> {
    const userFriendsSet = await this.feedService.getUserFriends(user);

    if (feed.author.id != user.id) {
      if (!userFriendsSet.has(feed.author.id)) {
        return false;
      }
    }

    return true;
  }
}
