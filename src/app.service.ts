import { Injectable } from '@nestjs/common';
import { InteractionEmojiEntity } from './models/feed-entity';
import { Repository, Not } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import {
  GetAllEmojisResDTO,
  GetAllQuestTypesResDTO,
  GetAllRolesResDTO,
} from './app-dto';
import { QuestTypesEntity } from './models/quest-entity';
import { RoleEntity } from './models/user-entity';
import { RoleDto } from './user/user-dto';

@Injectable()
export class AppService {
  constructor(
    @InjectRepository(InteractionEmojiEntity)
    private readonly emojiRepo: Repository<InteractionEmojiEntity>,

    @InjectRepository(RoleEntity)
    private readonly roleRepo: Repository<RoleEntity>,

    @InjectRepository(QuestTypesEntity)
    private readonly questTypeRepo: Repository<QuestTypesEntity>,
  ) {}

  async getAllInteractionEmojis(): Promise<GetAllEmojisResDTO> {
    const emojis = await this.emojiRepo.find();

    return {
      error: false,
      nbHits: emojis.length,
      emojis,
    };
  }

  async getAllQuestTypes(): Promise<GetAllQuestTypesResDTO> {
    const questTypes = await this.questTypeRepo.find();

    return {
      error: false,
      nbHits: questTypes.length,
      questTypes: questTypes,
    };
  }

  async getAllRoles(): Promise<GetAllRolesResDTO> {
    const roles = await this.roleRepo.find();

    const rolesResp = roles.map((item) => RoleDto.transform(item));

    return {
      error: false,
      nbHits: rolesResp.length,
      roles: rolesResp,
    };
  }
}
